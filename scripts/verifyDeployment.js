/**
 * 🔍 UbaSwap Deployment Verification Script
 *
 * This script verifies that all contracts are properly deployed and configured
 * on the Sepolia testnet, and tests basic functionality.
 */

const hre = require("hardhat");

// Contract addresses from deployment
const CONTRACT_ADDRESSES = {
    UBA_TOKEN: '******************************************',
    SWAP_AND_FORWARD: '******************************************',
    FEE_DISTRIBUTOR: '******************************************',
    WETH: '******************************************',
    UNISWAP_ROUTER: '******************************************',
    UNISWAP_QUOTER: '******************************************'
};

// Additional token addresses for testing
const TEST_TOKENS = {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************'
};

async function main() {
    console.log("🔍 Starting UbaSwap Deployment Verification...\n");

    const [deployer] = await hre.ethers.getSigners();
    console.log("📋 Verifying with account:", deployer.address);
    const balance = await hre.ethers.provider.getBalance(deployer.address);
    console.log("💰 Account balance:", hre.ethers.utils.formatEther(balance), "ETH\n");

    // Verify network
    const network = await hre.ethers.provider.getNetwork();
    console.log("🌐 Network:", network.name, "| Chain ID:", network.chainId);

    if (network.chainId !== ********) {
        throw new Error("❌ Not connected to Sepolia testnet!");
    }

    console.log("✅ Connected to Sepolia testnet\n");

    // Verify contract deployments
    await verifyContractDeployments();

    // Verify contract configurations
    await verifyContractConfigurations();

    // Test basic functionality
    await testBasicFunctionality();

    // Verify token integrations
    await verifyTokenIntegrations();

    console.log("\n🎉 Deployment verification completed successfully!");
    console.log("\n📋 Summary:");
    console.log("✅ All contracts deployed and verified");
    console.log("✅ Contract configurations correct");
    console.log("✅ Basic functionality working");
    console.log("✅ Token integrations verified");
    console.log("\n🚀 UbaSwap DEX is ready for testing!");
}

async function verifyContractDeployments() {
    console.log("🔍 Verifying Contract Deployments...");

    // Check UBA Token
    try {
        const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACT_ADDRESSES.UBA_TOKEN);
        const name = await ubaToken.name();
        const symbol = await ubaToken.symbol();
        const totalSupply = await ubaToken.totalSupply();

        console.log(`✅ UBA Token: ${name} (${symbol})`);
        console.log(`   Address: ${CONTRACT_ADDRESSES.UBA_TOKEN}`);
        console.log(`   Total Supply: ${hre.ethers.utils.formatEther(totalSupply)} UBA`);
    } catch (error) {
        console.error("❌ UBA Token verification failed:", error.message);
        throw error;
    }

    // Check SwapAndForward
    try {
        const swapAndForward = await hre.ethers.getContractAt("SwapAndForward", CONTRACT_ADDRESSES.SWAP_AND_FORWARD);
        const protocolFeeBps = await swapAndForward.protocolFeeBps();
        const feeDistributor = await swapAndForward.feeDistributor();

        console.log(`✅ SwapAndForward Contract`);
        console.log(`   Address: ${CONTRACT_ADDRESSES.SWAP_AND_FORWARD}`);
        console.log(`   Protocol Fee: ${protocolFeeBps} BPS (${protocolFeeBps/100}%)`);
        console.log(`   Fee Distributor: ${feeDistributor}`);
    } catch (error) {
        console.error("❌ SwapAndForward verification failed:", error.message);
        throw error;
    }

    // Check FeeDistributor
    try {
        const feeDistributor = await hre.ethers.getContractAt("FeeDistributor", CONTRACT_ADDRESSES.FEE_DISTRIBUTOR);
        const minimumProcessAmount = await feeDistributor.minimumProcessAmount();

        console.log(`✅ FeeDistributor Contract`);
        console.log(`   Address: ${CONTRACT_ADDRESSES.FEE_DISTRIBUTOR}`);
        console.log(`   Minimum Process Amount: ${hre.ethers.utils.formatEther(minimumProcessAmount)} ETH`);
    } catch (error) {
        console.error("❌ FeeDistributor verification failed:", error.message);
        throw error;
    }

    console.log("");
}

async function verifyContractConfigurations() {
    console.log("⚙️ Verifying Contract Configurations...");

    const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACT_ADDRESSES.UBA_TOKEN);
    const swapAndForward = await hre.ethers.getContractAt("SwapAndForward", CONTRACT_ADDRESSES.SWAP_AND_FORWARD);
    const feeDistributor = await hre.ethers.getContractAt("FeeDistributor", CONTRACT_ADDRESSES.FEE_DISTRIBUTOR);

    // Verify UBA Token configuration
    const owner = await ubaToken.owner();
    const maxSupply = await ubaToken.maxSupply();
    console.log(`✅ UBA Token Owner: ${owner}`);
    console.log(`✅ UBA Token Max Supply: ${hre.ethers.utils.formatEther(maxSupply)} UBA`);

    // Verify SwapAndForward configuration
    const swapFeeDistributor = await swapAndForward.feeDistributor();
    const uniswapRouter = await swapAndForward.uniswapRouter();
    console.log(`✅ SwapAndForward Fee Distributor: ${swapFeeDistributor}`);
    console.log(`✅ SwapAndForward Uniswap Router: ${uniswapRouter}`);

    // Verify FeeDistributor configuration
    const feeUbaToken = await feeDistributor.ubaToken();
    const feeUniswapRouter = await feeDistributor.uniswapRouter();
    console.log(`✅ FeeDistributor UBA Token: ${feeUbaToken}`);
    console.log(`✅ FeeDistributor Uniswap Router: ${feeUniswapRouter}`);

    // Verify cross-contract references
    if (swapFeeDistributor.toLowerCase() !== CONTRACT_ADDRESSES.FEE_DISTRIBUTOR.toLowerCase()) {
        throw new Error("❌ SwapAndForward fee distributor mismatch!");
    }

    if (feeUbaToken.toLowerCase() !== CONTRACT_ADDRESSES.UBA_TOKEN.toLowerCase()) {
        throw new Error("❌ FeeDistributor UBA token mismatch!");
    }

    console.log("✅ All contract configurations verified\n");
}

async function testBasicFunctionality() {
    console.log("🧪 Testing Basic Functionality...");

    const [deployer] = await hre.ethers.getSigners();
    const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACT_ADDRESSES.UBA_TOKEN);
    const swapAndForward = await hre.ethers.getContractAt("SwapAndForward", CONTRACT_ADDRESSES.SWAP_AND_FORWARD);

    // Test UBA Token basic functions
    try {
        const balance = await ubaToken.balanceOf(deployer.address);
        console.log(`✅ UBA Token balance query: ${hre.ethers.utils.formatEther(balance)} UBA`);

        const allowance = await ubaToken.allowance(deployer.address, CONTRACT_ADDRESSES.SWAP_AND_FORWARD);
        console.log(`✅ UBA Token allowance query: ${hre.ethers.utils.formatEther(allowance)} UBA`);
    } catch (error) {
        console.error("❌ UBA Token basic function test failed:", error.message);
    }

    // Test SwapAndForward view functions
    try {
        const protocolFeeBps = await swapAndForward.protocolFeeBps();
        console.log(`✅ SwapAndForward protocol fee: ${protocolFeeBps} BPS`);
    } catch (error) {
        console.error("❌ SwapAndForward basic function test failed:", error.message);
    }

    console.log("✅ Basic functionality tests passed\n");
}

async function verifyTokenIntegrations() {
    console.log("🪙 Verifying Token Integrations...");

    // Test external token contracts
    const tokens = [
        { name: "WETH", address: TEST_TOKENS.WETH },
        { name: "USDC", address: TEST_TOKENS.USDC },
        { name: "USDT", address: TEST_TOKENS.USDT },
        { name: "DAI", address: TEST_TOKENS.DAI }
    ];

    for (const token of tokens) {
        try {
            // Check if contract exists and has basic ERC20 functions
            const code = await hre.ethers.provider.getCode(token.address);
            if (code === "0x") {
                console.warn(`⚠️ ${token.name} contract not found at ${token.address}`);
                continue;
            }

            // For tokens that support name/symbol/decimals
            try {
                const tokenWithMetadata = await hre.ethers.getContractAt("IERC20Metadata", token.address);
                const name = await tokenWithMetadata.name();
                const symbol = await tokenWithMetadata.symbol();
                const decimals = await tokenWithMetadata.decimals();

                console.log(`✅ ${token.name}: ${name} (${symbol}) - ${decimals} decimals`);
                console.log(`   Address: ${token.address}`);
            } catch (metadataError) {
                console.log(`✅ ${token.name}: Contract exists (metadata not available)`);
                console.log(`   Address: ${token.address}`);
            }

        } catch (error) {
            console.error(`❌ ${token.name} verification failed:`, error.message);
        }
    }

    console.log("✅ Token integration verification completed\n");
}

// Helper function to check if contract exists
async function contractExists(address) {
    const code = await hre.ethers.provider.getCode(address);
    return code !== "0x";
}

// Run verification
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Verification failed:", error);
        process.exit(1);
    });
