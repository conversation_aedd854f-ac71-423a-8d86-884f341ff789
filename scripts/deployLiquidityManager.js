const hre = require("hardhat");

async function main() {
    console.log("🚀 Deploying LiquidityManager...\n");

    // Get deployer
    const [deployer] = await hre.ethers.getSigners();
    console.log("📋 Deploying with account:", deployer.address);

    // Contract addresses on Sepolia
    const POSITION_MANAGER = "******************************************"; // Uniswap V3 Position Manager
    const WETH = "******************************************";
    const FEE_DISTRIBUTOR = process.env.FEE_DISTRIBUTOR_ADDRESS;

    console.log("📋 Using addresses:");
    console.log("   Position Manager:", POSITION_MANAGER);
    console.log("   WETH:", WETH);
    console.log("   Fee Distributor:", FEE_DISTRIBUTOR);

    // Deploy LiquidityManager
    console.log("\n🔄 Deploying LiquidityManager...");
    const LiquidityManager = await hre.ethers.getContractFactory("LiquidityManager");
    const liquidityManager = await LiquidityManager.deploy(
        POSITION_MANAGER,
        WETH,
        FEE_DISTRIBUTOR
    );

    await liquidityManager.waitForDeployment();

    console.log("✅ LiquidityManager deployed to:", await liquidityManager.getAddress());

    // Verify deployment
    console.log("\n🔍 Verifying deployment...");
    try {
        const positionManager = await liquidityManager.positionManager();
        const weth = await liquidityManager.WETH();
        const feeDistributor = await liquidityManager.feeDistributor();
        const protocolFeeBps = await liquidityManager.protocolFeeBps();

        console.log("✅ Position Manager:", positionManager);
        console.log("✅ WETH:", weth);
        console.log("✅ Fee Distributor:", feeDistributor);
        console.log("✅ Protocol Fee:", protocolFeeBps.toString(), "BPS");

    } catch (error) {
        console.error("❌ Verification failed:", error.message);
    }

    // Update .env file
    console.log("\n📝 Updating .env file...");
    const fs = require('fs');
    const envPath = '.env';
    let envContent = fs.readFileSync(envPath, 'utf8');

    const liquidityManagerAddress = await liquidityManager.getAddress();

    // Add or update LIQUIDITY_MANAGER_ADDRESS
    if (envContent.includes('LIQUIDITY_MANAGER_ADDRESS=')) {
        envContent = envContent.replace(
            /LIQUIDITY_MANAGER_ADDRESS=.*/,
            `LIQUIDITY_MANAGER_ADDRESS=${liquidityManagerAddress}`
        );
    } else {
        envContent += `\nLIQUIDITY_MANAGER_ADDRESS=${liquidityManagerAddress}`;
    }

    // Add frontend env var
    if (envContent.includes('REACT_APP_LIQUIDITY_MANAGER_ADDRESS=')) {
        envContent = envContent.replace(
            /REACT_APP_LIQUIDITY_MANAGER_ADDRESS=.*/,
            `REACT_APP_LIQUIDITY_MANAGER_ADDRESS=${liquidityManagerAddress}`
        );
    } else {
        envContent += `\nREACT_APP_LIQUIDITY_MANAGER_ADDRESS=${liquidityManagerAddress}`;
    }

    fs.writeFileSync(envPath, envContent);

    console.log("\n🎉 Deployment Summary:");
    console.log("=====================================");
    console.log("✅ LiquidityManager:", liquidityManagerAddress);
    console.log("✅ Position Manager:", POSITION_MANAGER);
    console.log("✅ WETH:", WETH);
    console.log("✅ Fee Distributor:", FEE_DISTRIBUTOR);
    console.log("✅ Protocol Fee: 0.5%");
    console.log("=====================================");

    console.log("\n📋 Next Steps:");
    console.log("1. Update frontend configuration");
    console.log("2. Test add liquidity functionality");
    console.log("3. Test remove liquidity functionality");
    console.log("4. Test fee collection");

    // Verify on Etherscan (optional)
    if (process.env.ETHERSCAN_API_KEY) {
        console.log("\n🔍 Verifying on Etherscan...");
        try {
            await hre.run("verify:verify", {
                address: liquidityManagerAddress,
                constructorArguments: [
                    POSITION_MANAGER,
                    WETH,
                    FEE_DISTRIBUTOR
                ],
            });
            console.log("✅ Contract verified on Etherscan");
        } catch (error) {
            console.log("⚠️ Etherscan verification failed:", error.message);
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    });
