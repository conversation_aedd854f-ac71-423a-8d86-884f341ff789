const hre = require("hardhat");

// Contract addresses
const CONTRACTS = {
    UBA_TOKEN: '******************************************',
    SWAP_AND_FORWARD: '******************************************',
    FEE_DISTRIBUTOR: '******************************************',
    LIQUIDITY_MANAGER: '******************************************',
    WETH: '******************************************'
};

// Test tokens with trending Sepolia addresses
const TEST_TOKENS = {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    LINK: '******************************************',
    UNI: '******************************************',
    AAVE: '******************************************',
    MATIC: '******************************************',
    CRV: '******************************************'
};

async function main() {
    console.log("🧪 Testing UbaSwap with Frontend Integration...\n");

    // Get signer
    const [signer] = await hre.ethers.getSigners();
    console.log("📋 Testing with account:", signer.address);
    
    // Check balance
    const balance = await hre.ethers.provider.getBalance(signer.address);
    console.log("💰 ETH Balance:", hre.ethers.formatEther(balance), "ETH");

    // Test 1: Verify all contracts
    await verifyContracts(signer);

    // Test 2: Test token interactions
    await testTokenInteractions(signer);

    // Test 3: Test swap functionality
    await testSwapFunctionality(signer);

    // Test 4: Test liquidity functionality
    await testLiquidityFunctionality(signer);

    // Test 5: Generate frontend summary
    await generateFrontendSummary();

    console.log("\n🎉 All tests completed!");
    console.log("\n📋 Frontend Ready:");
    console.log("🌐 Open index.html in your browser");
    console.log("🔗 Connect your wallet");
    console.log("🔄 Test all swap combinations");
    console.log("🏊 Test add liquidity");
    console.log("🔍 Test token search and import");
}

async function verifyContracts(signer) {
    console.log("\n🔍 Verifying Contract Deployments...");

    try {
        // Test UBA Token
        const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACTS.UBA_TOKEN, signer);
        const name = await ubaToken.name();
        const symbol = await ubaToken.symbol();
        const totalSupply = await ubaToken.totalSupply();
        console.log(`✅ UBA Token: ${name} (${symbol}) - Supply: ${hre.ethers.formatEther(totalSupply)}`);

        // Test SwapAndForward
        const swapAndForward = await hre.ethers.getContractAt("SwapAndForward", CONTRACTS.SWAP_AND_FORWARD, signer);
        const protocolFeeBps = await swapAndForward.protocolFeeBps();
        console.log(`✅ SwapAndForward: Protocol Fee ${protocolFeeBps} BPS (${protocolFeeBps/100}%)`);

        // Test LiquidityManager
        const liquidityManager = await hre.ethers.getContractAt("LiquidityManager", CONTRACTS.LIQUIDITY_MANAGER, signer);
        const positionManager = await liquidityManager.positionManager();
        console.log(`✅ LiquidityManager: Position Manager ${positionManager}`);

        // Test FeeDistributor
        const feeDistributor = await hre.ethers.getContractAt("FeeDistributor", CONTRACTS.FEE_DISTRIBUTOR, signer);
        const minimumProcessAmount = await feeDistributor.minimumProcessAmount();
        console.log(`✅ FeeDistributor: Min Process ${hre.ethers.formatEther(minimumProcessAmount)} ETH`);

        console.log("✅ All contracts verified and accessible");

    } catch (error) {
        console.error("❌ Contract verification failed:", error.message);
    }
}

async function testTokenInteractions(signer) {
    console.log("\n🪙 Testing Token Interactions...");

    try {
        // Test UBA Token balance and operations
        const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACTS.UBA_TOKEN, signer);
        const ubaBalance = await ubaToken.balanceOf(signer.address);
        console.log(`UBA Balance: ${hre.ethers.formatEther(ubaBalance)}`);

        // Test token approvals
        const allowance = await ubaToken.allowance(signer.address, CONTRACTS.SWAP_AND_FORWARD);
        console.log(`UBA Allowance for SwapAndForward: ${hre.ethers.formatEther(allowance)}`);

        // Test external tokens (check if they exist)
        for (const [symbol, address] of Object.entries(TEST_TOKENS)) {
            try {
                const code = await hre.ethers.provider.getCode(address);
                if (code !== "0x") {
                    console.log(`✅ ${symbol}: Contract exists at ${address}`);
                    
                    // Try to get token info
                    try {
                        const tokenContract = await hre.ethers.getContractAt("IERC20Metadata", address, signer);
                        const tokenSymbol = await tokenContract.symbol();
                        const tokenName = await tokenContract.name();
                        const decimals = await tokenContract.decimals();
                        console.log(`   ${tokenName} (${tokenSymbol}) - ${decimals} decimals`);
                    } catch (metaError) {
                        console.log(`   Basic ERC20 contract (metadata not available)`);
                    }
                } else {
                    console.log(`⚠️ ${symbol}: No contract at ${address}`);
                }
            } catch (error) {
                console.log(`❌ ${symbol}: Error checking contract`);
            }
        }

        console.log("✅ Token interaction tests completed");

    } catch (error) {
        console.error("❌ Token interaction test failed:", error.message);
    }
}

async function testSwapFunctionality(signer) {
    console.log("\n🔄 Testing Swap Functionality...");

    try {
        const swapAndForward = await hre.ethers.getContractAt("SwapAndForward", CONTRACTS.SWAP_AND_FORWARD, signer);
        
        // Test small ETH to UBA swap
        const amountIn = hre.ethers.parseEther("0.001"); // 0.001 ETH
        const deadline = Math.floor(Date.now() / 1000) + 1200; // 20 minutes
        const fee = 3000; // 0.3%

        console.log("🔄 Testing ETH to UBA swap...");
        console.log(`Amount: ${hre.ethers.formatEther(amountIn)} ETH`);

        // Check if we have enough ETH
        const ethBalance = await hre.ethers.provider.getBalance(signer.address);
        if (ethBalance < (amountIn * 2n)) {
            console.log("⚠️ Insufficient ETH for swap test");
            console.log("💡 Get more ETH from Sepolia faucet: https://sepoliafaucet.com");
            return;
        }

        const tx = await swapAndForward.swapAndForwardSingleHop(
            hre.ethers.ZeroAddress, // ETH
            amountIn,
            CONTRACTS.UBA_TOKEN, // UBA
            0, // Accept any amount
            signer.address,
            fee,
            deadline,
            { value: amountIn }
        );

        console.log("⏳ Waiting for transaction...");
        const receipt = await tx.wait();
        console.log(`✅ Swap successful! Gas used: ${receipt.gasUsed.toString()}`);
        console.log(`📋 Transaction hash: ${receipt.transactionHash}`);

        // Check new balances
        const newEthBalance = await hre.ethers.provider.getBalance(signer.address);
        const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACTS.UBA_TOKEN, signer);
        const newUbaBalance = await ubaToken.balanceOf(signer.address);
        
        console.log(`New ETH Balance: ${hre.ethers.formatEther(newEthBalance)}`);
        console.log(`New UBA Balance: ${hre.ethers.formatEther(newUbaBalance)}`);

    } catch (error) {
        console.error("❌ Swap test failed:", error.message);
        if (error.message.includes("insufficient funds")) {
            console.log("💡 Tip: Get more ETH from Sepolia faucet");
        } else if (error.message.includes("swap Uniswap gagal")) {
            console.log("💡 Tip: Pool might not exist or have insufficient liquidity");
        }
    }
}

async function testLiquidityFunctionality(signer) {
    console.log("\n🏊 Testing Liquidity Functionality...");

    try {
        const liquidityManager = await hre.ethers.getContractAt("LiquidityManager", CONTRACTS.LIQUIDITY_MANAGER, signer);
        const ubaToken = await hre.ethers.getContractAt("UBAToken", CONTRACTS.UBA_TOKEN, signer);

        // Check balances
        const ethBalance = await hre.ethers.provider.getBalance(signer.address);
        const ubaBalance = await ubaToken.balanceOf(signer.address);

        console.log(`ETH Balance: ${hre.ethers.formatEther(ethBalance)}`);
        console.log(`UBA Balance: ${hre.ethers.formatEther(ubaBalance)}`);

        // Check if we have enough for liquidity
        if (ethBalance < hre.ethers.parseEther("0.01") || ubaBalance < hre.ethers.parseEther("1")) {
            console.log("⚠️ Insufficient balance for liquidity test");
            console.log("💡 Tip: Do some swaps first to get UBA tokens");
            console.log("✅ LiquidityManager contract is ready for use");
            return;
        }

        // Test liquidity manager functions
        console.log("🔍 Testing LiquidityManager functions...");
        
        // Get user positions
        const positions = await liquidityManager.getUserPositions(signer.address);
        console.log(`Current positions: ${positions.length}`);

        // Check protocol fee
        const protocolFeeBps = await liquidityManager.protocolFeeBps();
        console.log(`Protocol fee: ${protocolFeeBps} BPS (${protocolFeeBps/100}%)`);

        console.log("✅ LiquidityManager ready for frontend integration");

    } catch (error) {
        console.error("❌ Liquidity test failed:", error.message);
    }
}

async function generateFrontendSummary() {
    console.log("\n📋 Frontend Integration Summary");
    console.log("=====================================");
    
    console.log("\n🔧 Contract Addresses:");
    Object.entries(CONTRACTS).forEach(([name, address]) => {
        console.log(`${name}: ${address}`);
    });
    
    console.log("\n🪙 Supported Tokens:");
    console.log("ETH (Native)");
    console.log(`UBA: ${CONTRACTS.UBA_TOKEN}`);
    Object.entries(TEST_TOKENS).forEach(([symbol, address]) => {
        console.log(`${symbol}: ${address}`);
    });
    
    console.log("\n🎯 Available Features:");
    console.log("✅ Token Swaps (ETH ↔ UBA and all token pairs)");
    console.log("✅ Add Liquidity (with LiquidityManager contract)");
    console.log("✅ Remove Liquidity (framework ready)");
    console.log("✅ Swap & Forward (send to different address)");
    console.log("✅ Fee Processing (collect and burn)");
    console.log("✅ Token Search & Import (custom tokens)");
    console.log("✅ Real-time Balance Updates");
    console.log("✅ Transaction Status Tracking");
    
    console.log("\n🌐 Frontend Files:");
    console.log("📄 index.html - Main application");
    console.log("🎨 frontend/styles.css - Styling");
    console.log("⚙️ frontend/config.js - Configuration");
    console.log("🔄 frontend/swap.js - Swap functionality");
    console.log("🏊 frontend/liquidityNew.js - Enhanced liquidity");
    console.log("🔗 frontend/wallet.js - Wallet connection");
    console.log("🖥️ frontend/ui.js - UI management");
    
    console.log("\n🚀 How to Use:");
    console.log("1. Open index.html in your browser");
    console.log("2. Connect MetaMask wallet (Sepolia network)");
    console.log("3. Test swaps between different tokens");
    console.log("4. Add liquidity to earn fees");
    console.log("5. Use token search to import custom tokens");
    console.log("6. Monitor fees and process them");
    
    console.log("\n💡 Tips:");
    console.log("• Get Sepolia ETH from: https://sepoliafaucet.com");
    console.log("• Get test tokens from respective faucets");
    console.log("• Start with small amounts for testing");
    console.log("• Check console for detailed transaction info");
    
    console.log("=====================================");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Test failed:", error);
        process.exit(1);
    });
