<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦄 UbaSwap - Decentralized Exchange</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="frontend/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦄</text></svg>">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🦄</span>
                    <span class="logo-text">UbaSwap</span>
                </div>

                <nav class="nav">
                    <a href="#" class="nav-link active" data-page="swap">Swap</a>
                    <a href="#" class="nav-link" data-page="liquidity">Liquidity</a>
                    <a href="#" class="nav-link" data-page="forward">Swap & Forward</a>
                    <a href="#" class="nav-link" data-page="fees">Fees</a>
                </nav>

                <div class="header-actions">
                    <button class="settings-btn" onclick="window.uiManager.openSettings()">⚙️</button>
                    <button id="connectWallet" class="connect-btn">
                        <span class="connect-text">Connect Wallet</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Swap Page -->
            <div id="swapPage" class="page active">
                <div class="page-header">
                    <h1>🔄 Swap Tokens</h1>
                    <p>Trade tokens instantly with low fees</p>
                </div>

                <div class="swap-container">
                    <div class="swap-card">
                        <div class="swap-header">
                            <h3>Swap</h3>
                            <button class="settings-btn" onclick="window.uiManager.openSettings()">⚙️</button>
                        </div>

                        <div class="swap-form">
                            <!-- From Token -->
                            <div class="token-input-group">
                                <label>From</label>
                                <div class="token-input">
                                    <input type="number" id="fromAmount" placeholder="0.0" step="any">
                                    <button class="token-select" onclick="window.uiManager.openTokenSelector('from')">
                                        <span class="token-icon">Ξ</span>
                                        <span class="token-symbol">ETH</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                                <div class="token-balance">
                                    Balance: <span id="fromBalance">0.0000</span>
                                    <button class="max-btn" onclick="window.uiManager.setMaxAmount('from')">MAX</button>
                                </div>
                            </div>

                            <!-- Swap Direction Button -->
                            <div class="swap-direction">
                                <button class="swap-arrow" onclick="window.uiManager.swapTokenDirection()">
                                    ⇅
                                </button>
                            </div>

                            <!-- To Token -->
                            <div class="token-input-group">
                                <label>To</label>
                                <div class="token-input">
                                    <input type="number" id="toAmount" placeholder="0.0" step="any" readonly>
                                    <button class="token-select" onclick="window.uiManager.openTokenSelector('to')">
                                        <span class="token-icon">🦄</span>
                                        <span class="token-symbol">UBA</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                                <div class="token-balance">
                                    Balance: <span id="toBalance">0.0000</span>
                                </div>
                            </div>

                            <!-- Swap Info -->
                            <div class="swap-info">
                                <div class="info-row">
                                    <span>Rate</span>
                                    <span id="swapRate">-</span>
                                </div>
                                <div class="info-row">
                                    <span>Protocol Fee</span>
                                    <span>0.5%</span>
                                </div>
                                <div class="info-row">
                                    <span>Slippage</span>
                                    <span id="currentSlippage">0.5%</span>
                                </div>
                            </div>

                            <!-- Swap Button -->
                            <button id="swapButton" class="swap-btn" onclick="window.uiManager.executeSwap()">
                                Connect Wallet
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liquidity Page -->
            <div id="liquidityPage" class="page">
                <div class="page-header">
                    <h1>🏊 Liquidity Pools</h1>
                    <p>Add liquidity to earn fees</p>
                </div>

                <div class="liquidity-container">
                    <div class="liquidity-tabs">
                        <button class="tab-btn active" data-tab="add">Add Liquidity</button>
                        <button class="tab-btn" data-tab="remove">Remove Liquidity</button>
                        <button class="tab-btn" data-tab="positions">My Positions</button>
                    </div>

                    <!-- Add Liquidity Tab -->
                    <div id="addLiquidityTab" class="tab-content active">
                        <div class="liquidity-card">
                            <h3>Add Liquidity</h3>

                            <div class="token-pair-inputs">
                                <div class="token-input-group">
                                    <label>Token A</label>
                                    <div class="token-input">
                                        <input type="number" id="tokenAAmount" placeholder="0.0" step="any">
                                        <button class="token-select" onclick="window.uiManager.openTokenSelector('tokenA')">
                                            <span class="token-icon">Ξ</span>
                                            <span class="token-symbol">ETH</span>
                                            <span class="dropdown-arrow">▼</span>
                                        </button>
                                    </div>
                                    <div class="token-balance">
                                        Balance: <span id="tokenABalance">0.0000</span>
                                    </div>
                                </div>

                                <div class="plus-icon">+</div>

                                <div class="token-input-group">
                                    <label>Token B</label>
                                    <div class="token-input">
                                        <input type="number" id="tokenBAmount" placeholder="0.0" step="any">
                                        <button class="token-select" onclick="window.uiManager.openTokenSelector('tokenB')">
                                            <span class="token-icon">🦄</span>
                                            <span class="token-symbol">UBA</span>
                                            <span class="dropdown-arrow">▼</span>
                                        </button>
                                    </div>
                                    <div class="token-balance">
                                        Balance: <span id="tokenBBalance">0.0000</span>
                                    </div>
                                </div>
                            </div>

                            <div class="price-range">
                                <h4>Price Range</h4>
                                <div class="range-inputs">
                                    <div class="range-input">
                                        <label>Min Price</label>
                                        <input type="number" id="minPrice" placeholder="0.0" step="any">
                                    </div>
                                    <div class="range-input">
                                        <label>Max Price</label>
                                        <input type="number" id="maxPrice" placeholder="0.0" step="any">
                                    </div>
                                </div>
                            </div>

                            <button id="addLiquidityButton" class="action-btn" onclick="window.enhancedLiquidityManager.addLiquidity()">
                                Add Liquidity
                            </button>
                        </div>
                    </div>

                    <!-- Remove Liquidity Tab -->
                    <div id="removeLiquidityTab" class="tab-content">
                        <div class="liquidity-card">
                            <h3>Remove Liquidity</h3>
                            <p>Select a position to remove liquidity</p>

                            <div id="positionsList" class="positions-list">
                                <!-- Positions will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- My Positions Tab -->
                    <div id="positionsTab" class="tab-content">
                        <div class="liquidity-card">
                            <h3>My Positions</h3>

                            <div id="userPositions" class="positions-grid">
                                <!-- User positions will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Swap & Forward Page -->
            <div id="forwardPage" class="page">
                <div class="page-header">
                    <h1>🔄➡️ Swap & Forward</h1>
                    <p>Swap tokens and send to another address</p>
                </div>

                <div class="forward-container">
                    <div class="forward-card">
                        <div class="forward-form">
                            <!-- Recipient Address -->
                            <div class="input-group">
                                <label>Recipient Address</label>
                                <input type="text" id="recipientAddress" placeholder="0x..." class="address-input">
                            </div>

                            <!-- From Token -->
                            <div class="token-input-group">
                                <label>From</label>
                                <div class="token-input">
                                    <input type="number" id="forwardFromAmount" placeholder="0.0" step="any">
                                    <button class="token-select" onclick="window.uiManager.openTokenSelector('forwardFrom')">
                                        <span class="token-icon">Ξ</span>
                                        <span class="token-symbol">ETH</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                                <div class="token-balance">
                                    Balance: <span id="forwardFromBalance">0.0000</span>
                                    <button class="max-btn" onclick="window.uiManager.setMaxAmount('forwardFrom')">MAX</button>
                                </div>
                            </div>

                            <!-- To Token -->
                            <div class="token-input-group">
                                <label>To</label>
                                <div class="token-input">
                                    <input type="number" id="forwardToAmount" placeholder="0.0" step="any" readonly>
                                    <button class="token-select" onclick="window.uiManager.openTokenSelector('forwardTo')">
                                        <span class="token-icon">🦄</span>
                                        <span class="token-symbol">UBA</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                            </div>

                            <button id="forwardButton" class="action-btn" onclick="window.uiManager.executeSwapAndForward()">
                                Swap & Forward
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fees Page -->
            <div id="feesPage" class="page">
                <div class="page-header">
                    <h1>💰 Fee Management</h1>
                    <p>View and process collected fees</p>
                </div>

                <div class="fees-container">
                    <div class="fees-stats">
                        <div class="stat-card">
                            <h3>ETH Fees</h3>
                            <div class="stat-value" id="ethFeesCollected">0.0000</div>
                        </div>
                        <div class="stat-card">
                            <h3>UBA Fees</h3>
                            <div class="stat-value" id="ubaFeesCollected">0.0000</div>
                        </div>
                        <div class="stat-card">
                            <h3>Total Value</h3>
                            <div class="stat-value" id="totalFeesValue">$0.00</div>
                        </div>
                    </div>

                    <div class="fees-actions">
                        <button class="action-btn" onclick="window.uiManager.processFees()">
                            Process Fees
                        </button>
                        <button class="action-btn secondary" onclick="window.uiManager.refreshFees()">
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Token Selector Modal -->
        <div id="tokenModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Select Token</h3>
                    <button class="close-btn" onclick="window.uiManager.closeTokenModal()">×</button>
                </div>

                <div class="token-search">
                    <input type="text" id="tokenSearch" placeholder="Search by name, symbol, or address..." class="search-input">
                </div>

                <div class="token-list" id="tokenList">
                    <!-- Token list will be populated here -->
                </div>

                <div id="customTokenImport" class="custom-token-import" style="display: none;">
                    <!-- Custom token import will be shown here -->
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settingsModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="close-btn" onclick="window.uiManager.closeSettings()">×</button>
                </div>

                <div class="settings-content">
                    <div class="setting-group">
                        <label>Slippage Tolerance</label>
                        <div class="slippage-options">
                            <button class="slippage-btn" data-slippage="0.1">0.1%</button>
                            <button class="slippage-btn active" data-slippage="0.5">0.5%</button>
                            <button class="slippage-btn" data-slippage="1.0">1.0%</button>
                            <input type="number" id="customSlippage" placeholder="Custom" step="0.1" min="0" max="50">
                        </div>
                    </div>

                    <div class="setting-group">
                        <label>Transaction Deadline</label>
                        <div class="deadline-input">
                            <input type="number" id="deadlineInput" value="20" min="1" max="60">
                            <span>minutes</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusContainer" class="status-container">
            <!-- Status messages will appear here -->
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p id="loadingText">Processing...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="frontend/config.js"></script>
    <script src="frontend/contracts.js"></script>
    <script src="frontend/ui.js"></script>
    <script src="frontend/wallet.js"></script>
    <script src="frontend/swap.js"></script>
    <script src="frontend/liquidity.js"></script>
    <script src="frontend/liquidityNew.js"></script>

    <script>
        // Initialize the application
        window.addEventListener('DOMContentLoaded', async () => {
            console.log('🦄 UbaSwap Loading...');

            // Initialize UI Manager
            window.uiManager = new UIManager();

            // Initialize Wallet Manager
            window.walletManager = new WalletManager();

            // Initialize Swap Manager
            window.swapManager = new SwapManager();

            // Initialize Liquidity Manager
            window.liquidityManager = new LiquidityManager();

            // Initialize Enhanced Liquidity Manager
            window.enhancedLiquidityManager = new EnhancedLiquidityManager();

            console.log('✅ UbaSwap Ready!');

            // Auto-connect if previously connected
            if (localStorage.getItem('walletConnected') === 'true') {
                await window.walletManager.connectWallet();
            }
        });
    </script>
</body>
</html>
