# 🧪 UbaSwap DEX - Comprehensive Testing Guide

## 🎯 Overview

This guide provides step-by-step instructions for testing all UbaSwap DEX features, including the new tokens (WETH, USDC, USDT, DAI) and enhanced token search functionality.

## 🚀 Quick Start Testing

### 1. **Open Test Interface**
```bash
# Open the test page in your browser
open test-page.html
```

### 2. **Connect Wallet**
- Click "🔗 Connect Wallet"
- Approve MetaMask connection
- Ensure you're on Sepolia testnet

### 3. **Run All Tests**
- Click "🚀 Run All Tests" for comprehensive testing
- Or test individual features using specific buttons

## 🪙 Supported Tokens

| Token | Symbol | Address | Decimals | Type |
|-------|--------|---------|----------|------|
| Ethereum | ETH | Native | 18 | Native |
| UBA Token | UBA | `******************************************` | 18 | ERC20 |
| Wrapped Ether | WETH | `******************************************` | 18 | ERC20 |
| USD Coin | USDC | `******************************************` | 6 | ERC20 |
| Tether USD | USDT | `******************************************` | 6 | ERC20 |
| Dai Stablecoin | DAI | `******************************************` | 18 | ERC20 |

## 🔄 Feature Testing

### **1. Token Swaps**

#### **Basic Swap Testing**
```javascript
// Test ETH to UBA swap
await testSwap('ETH', 'UBA', '0.01');

// Test UBA to ETH swap  
await testSwap('UBA', 'ETH', '100');

// Test ETH to WETH swap
await testSwap('ETH', 'WETH', '0.01');

// Test WETH to USDC swap
await testSwap('WETH', 'USDC', '0.01');
```

#### **All Swap Combinations**
The system supports swaps between all token pairs:
- ETH ↔ UBA, WETH, USDC, USDT, DAI
- UBA ↔ ETH, WETH, USDC, USDT, DAI
- WETH ↔ ETH, UBA, USDC, USDT, DAI
- USDC ↔ ETH, UBA, WETH, USDT, DAI
- USDT ↔ ETH, UBA, WETH, USDC, DAI
- DAI ↔ ETH, UBA, WETH, USDC, USDT

#### **Approval Requirements**
- ETH swaps: No approval needed (native token)
- ERC20 swaps: Automatic approval handling
- Manual approval testing available

### **2. Liquidity Management**

#### **Add Liquidity**
```javascript
// Test add liquidity (simulated)
await testAddLiquidity();
```

#### **Remove Liquidity**
```javascript
// Test remove liquidity (simulated)
await testRemoveLiquidity();
```

*Note: Full liquidity management requires Uniswap V3 Position Manager integration*

### **3. Swap & Forward**

#### **Basic Forward Testing**
```javascript
// Test swap and forward to another address
await testSwapAndForward();
```

#### **Custom Recipient**
- Enter recipient address in the interface
- Test forwarding tokens to different addresses
- Verify recipient receives correct amounts

### **4. Fee Processing**

#### **Fee Collection Testing**
```javascript
// Check collected fees
await testFeeProcessing();

// View fees by token
const ethFees = await feeDistributor.collectedFees(ethers.constants.AddressZero);
const ubaFees = await feeDistributor.collectedFees(CONFIG.CONTRACTS.UBA_TOKEN);
```

#### **Fee Distribution**
- Admin-only functionality
- Converts collected fees to UBA tokens
- Burns UBA tokens (deflationary mechanism)

### **5. Token Search & Import**

#### **Search Functionality**
1. Open token selector modal
2. Type in search box:
   - Token symbol (e.g., "WETH")
   - Token name (e.g., "Wrapped Ether")
   - Contract address (e.g., "******************************************")

#### **Custom Token Import**
1. Paste contract address in search box
2. Click "Import Token" when prompted
3. Token automatically added to list
4. Test swaps with imported token

#### **Supported Import Features**
- ✅ Automatic token metadata detection
- ✅ Symbol, name, and decimals retrieval
- ✅ Balance display
- ✅ Swap integration
- ✅ Validation for invalid addresses

## 🛠️ Manual Testing Steps

### **Step 1: Environment Setup**
```bash
# Verify deployment
npx hardhat run scripts/verifyDeployment.js --network sepolia

# Check contract addresses
cat DEPLOYMENT_SUMMARY.md
```

### **Step 2: Wallet Preparation**
1. **Get Sepolia ETH**: Use [Sepolia Faucet](https://sepoliafaucet.com)
2. **Get Test Tokens**: Use respective faucets for USDC, USDT, DAI
3. **Check Balances**: Verify you have tokens for testing

### **Step 3: Frontend Testing**

#### **Using Old Frontend (HTML/JS)**
```bash
# Open in browser
open frontend/index-old.html
```

#### **Using New Frontend (React)**
```bash
cd frontend
npm install
npm run dev
```

### **Step 4: Feature Validation**

#### **Swap Testing Checklist**
- [ ] ETH to UBA swap works
- [ ] UBA to ETH swap works  
- [ ] ETH to WETH swap works
- [ ] WETH to USDC swap works
- [ ] USDC to USDT swap works
- [ ] DAI to ETH swap works
- [ ] Approval prompts appear for ERC20 tokens
- [ ] Transaction confirmations work
- [ ] Balance updates after swaps
- [ ] Fee collection occurs

#### **UI Testing Checklist**
- [ ] Token selector opens
- [ ] Search functionality works
- [ ] Custom token import works
- [ ] Balance display accurate
- [ ] MAX button works
- [ ] Swap direction toggle works
- [ ] Settings modal works
- [ ] Transaction status updates

#### **Error Handling Checklist**
- [ ] Insufficient balance errors
- [ ] Invalid address errors
- [ ] Network errors handled
- [ ] User rejection handled
- [ ] Slippage errors handled

## 🔧 Troubleshooting

### **Common Issues**

#### **"Transaction Failed"**
- Check gas limits
- Verify token balances
- Ensure proper approvals
- Check network connection

#### **"Token Not Found"**
- Verify contract address
- Check if token exists on Sepolia
- Ensure proper ERC20 implementation

#### **"Insufficient Allowance"**
- Approve token spending first
- Check approval transaction status
- Verify approval amount

#### **"Wrong Network"**
- Switch to Sepolia testnet
- Check MetaMask network settings
- Verify RPC endpoints

### **Debug Commands**

```javascript
// Check balances
await testBalances();

// Check approvals
await checkApprovals();

// Test specific swap
await testSwap('ETH', 'UBA', '0.01');

// View console logs
// Open browser developer tools
```

## 📊 Expected Results

### **Successful Swap**
- Transaction hash displayed
- Balance updates reflected
- Fee collection event emitted
- Gas usage reasonable (<200k gas)

### **Successful Token Import**
- Token metadata retrieved
- Token added to selector
- Balance displayed correctly
- Swap functionality enabled

### **Successful Fee Processing**
- Fees collected in contract
- Admin can process fees
- UBA tokens burned
- Events emitted correctly

## 🎯 Performance Benchmarks

| Operation | Expected Gas | Time | Success Rate |
|-----------|--------------|------|--------------|
| ETH→UBA Swap | ~150k gas | <30s | >95% |
| Token→Token Swap | ~180k gas | <45s | >90% |
| Token Approval | ~50k gas | <15s | >99% |
| Custom Import | 0 gas | <5s | >95% |

## 🚀 Next Steps

After successful testing:

1. **Deploy to Mainnet** (when ready)
2. **Add More Tokens** using import functionality
3. **Implement Advanced Features**:
   - Multi-hop swaps
   - Limit orders
   - Liquidity mining
   - Governance features

## 📞 Support

If you encounter issues:

1. Check this guide first
2. Review console logs
3. Verify contract addresses
4. Test on fresh browser session
5. Report bugs with transaction hashes

---

**Happy Testing! 🧪✨**
