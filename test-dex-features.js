/**
 * 🧪 UbaSwap DEX - Comprehensive Feature Testing Script
 * 
 * This script tests all DEX features including:
 * - Token swaps (ETH ↔ UBA, ETH ↔ WETH, etc.)
 * - Add/Remove Liquidity
 * - Swap & Forward functionality
 * - Fee processing and distribution
 * - Token approvals and balance checks
 * - Custom token import functionality
 */

class DEXTester {
    constructor() {
        this.testResults = [];
        this.provider = null;
        this.signer = null;
        this.contracts = {};
        this.userAddress = null;
        
        // Test configuration
        this.testAmounts = {
            small: ethers.utils.parseEther("0.01"),   // 0.01 ETH
            medium: ethers.utils.parseEther("0.1"),   // 0.1 ETH
            large: ethers.utils.parseEther("1.0")     // 1.0 ETH
        };
        
        this.testTokens = ['ETH', 'UBA', 'WETH', 'USDC', 'USDT', 'DAI'];
    }
    
    // Initialize testing environment
    async initialize() {
        try {
            console.log('🚀 Initializing DEX Testing Environment...');
            
            // Check if wallet is connected
            if (!window.ethereum) {
                throw new Error('MetaMask not detected');
            }
            
            this.provider = new ethers.providers.Web3Provider(window.ethereum);
            this.signer = this.provider.getSigner();
            this.userAddress = await this.signer.getAddress();
            
            // Initialize contracts
            await this.initializeContracts();
            
            console.log(`✅ Testing environment initialized for address: ${this.userAddress}`);
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize testing environment:', error);
            return false;
        }
    }
    
    // Initialize contract instances
    async initializeContracts() {
        this.contracts = {
            ubaToken: new ethers.Contract(
                CONFIG.CONTRACTS.UBA_TOKEN,
                CONTRACT_ABIS.UBAToken,
                this.signer
            ),
            swapAndForward: new ethers.Contract(
                CONFIG.CONTRACTS.SWAP_AND_FORWARD,
                CONTRACT_ABIS.SwapAndForward,
                this.signer
            ),
            feeDistributor: new ethers.Contract(
                CONFIG.CONTRACTS.FEE_DISTRIBUTOR,
                CONTRACT_ABIS.FeeDistributor,
                this.signer
            )
        };
        
        // Initialize token contracts for all supported tokens
        for (const [symbol, token] of Object.entries(CONFIG.TOKENS)) {
            if (!token.isNative) {
                this.contracts[`${symbol.toLowerCase()}Token`] = new ethers.Contract(
                    token.address,
                    CONTRACT_ABIS.ERC20,
                    this.signer
                );
            }
        }
    }
    
    // Run all tests
    async runAllTests() {
        console.log('🧪 Starting Comprehensive DEX Feature Testing...\n');
        
        const tests = [
            { name: 'Balance Checks', fn: () => this.testBalanceChecks() },
            { name: 'Token Approvals', fn: () => this.testTokenApprovals() },
            { name: 'ETH to UBA Swap', fn: () => this.testSwap('ETH', 'UBA', this.testAmounts.small) },
            { name: 'UBA to ETH Swap', fn: () => this.testSwap('UBA', 'ETH', this.testAmounts.small) },
            { name: 'ETH to WETH Swap', fn: () => this.testSwap('ETH', 'WETH', this.testAmounts.small) },
            { name: 'WETH to USDC Swap', fn: () => this.testSwap('WETH', 'USDC', this.testAmounts.small) },
            { name: 'Add Liquidity', fn: () => this.testAddLiquidity() },
            { name: 'Remove Liquidity', fn: () => this.testRemoveLiquidity() },
            { name: 'Swap & Forward', fn: () => this.testSwapAndForward() },
            { name: 'Fee Processing', fn: () => this.testFeeProcessing() },
            { name: 'Custom Token Import', fn: () => this.testCustomTokenImport() }
        ];
        
        for (const test of tests) {
            try {
                console.log(`🔄 Running test: ${test.name}...`);
                const result = await test.fn();
                this.testResults.push({ name: test.name, status: 'PASSED', result });
                console.log(`✅ ${test.name} - PASSED\n`);
            } catch (error) {
                this.testResults.push({ name: test.name, status: 'FAILED', error: error.message });
                console.error(`❌ ${test.name} - FAILED:`, error.message, '\n');
            }
        }
        
        this.printTestSummary();
    }
    
    // Test balance checks for all tokens
    async testBalanceChecks() {
        const balances = {};
        
        // Check ETH balance
        const ethBalance = await this.provider.getBalance(this.userAddress);
        balances.ETH = ethers.utils.formatEther(ethBalance);
        
        // Check token balances
        for (const [symbol, token] of Object.entries(CONFIG.TOKENS)) {
            if (!token.isNative) {
                try {
                    const contract = this.contracts[`${symbol.toLowerCase()}Token`];
                    const balance = await contract.balanceOf(this.userAddress);
                    balances[symbol] = ethers.utils.formatUnits(balance, token.decimals);
                } catch (error) {
                    balances[symbol] = 'Error: ' + error.message;
                }
            }
        }
        
        console.log('💰 Current Balances:', balances);
        return balances;
    }
    
    // Test token approvals
    async testTokenApprovals() {
        const approvals = {};
        
        for (const [symbol, token] of Object.entries(CONFIG.TOKENS)) {
            if (!token.isNative) {
                try {
                    const contract = this.contracts[`${symbol.toLowerCase()}Token`];
                    const allowance = await contract.allowance(
                        this.userAddress,
                        CONFIG.CONTRACTS.SWAP_AND_FORWARD
                    );
                    approvals[symbol] = ethers.utils.formatUnits(allowance, token.decimals);
                } catch (error) {
                    approvals[symbol] = 'Error: ' + error.message;
                }
            }
        }
        
        console.log('🔐 Current Approvals:', approvals);
        return approvals;
    }
    
    // Test token swap functionality
    async testSwap(tokenInSymbol, tokenOutSymbol, amount) {
        const tokenIn = CONFIG.TOKENS[tokenInSymbol];
        const tokenOut = CONFIG.TOKENS[tokenOutSymbol];
        
        console.log(`🔄 Testing swap: ${amount} ${tokenInSymbol} → ${tokenOutSymbol}`);
        
        // Get initial balances
        const initialBalances = await this.getTokenBalances([tokenInSymbol, tokenOutSymbol]);
        
        // Prepare swap parameters
        const tokenInAddress = tokenIn.isNative ? ethers.constants.AddressZero : tokenIn.address;
        const tokenOutAddress = tokenOut.isNative ? ethers.constants.AddressZero : tokenOut.address;
        const deadline = Math.floor(Date.now() / 1000) + 1200; // 20 minutes
        const fee = CONFIG.FEE_TIERS.MEDIUM; // 0.3%
        
        let tx;
        
        if (tokenIn.isNative) {
            // ETH to Token swap
            tx = await this.contracts.swapAndForward.swapAndForwardSingleHop(
                tokenInAddress,
                amount,
                tokenOutAddress,
                0, // Accept any amount for testing
                this.userAddress,
                fee,
                deadline,
                { value: amount }
            );
        } else {
            // Token to ETH/Token swap - need approval first
            const tokenContract = this.contracts[`${tokenInSymbol.toLowerCase()}Token`];
            const allowance = await tokenContract.allowance(
                this.userAddress,
                CONFIG.CONTRACTS.SWAP_AND_FORWARD
            );
            
            if (allowance.lt(amount)) {
                console.log('🔐 Approving token spend...');
                const approveTx = await tokenContract.approve(
                    CONFIG.CONTRACTS.SWAP_AND_FORWARD,
                    amount
                );
                await approveTx.wait();
            }
            
            tx = await this.contracts.swapAndForward.swapAndForwardSingleHop(
                tokenInAddress,
                amount,
                tokenOutAddress,
                0, // Accept any amount for testing
                this.userAddress,
                fee,
                deadline
            );
        }
        
        const receipt = await tx.wait();
        
        // Get final balances
        const finalBalances = await this.getTokenBalances([tokenInSymbol, tokenOutSymbol]);
        
        const result = {
            transactionHash: receipt.transactionHash,
            gasUsed: receipt.gasUsed.toString(),
            initialBalances,
            finalBalances,
            events: receipt.events?.map(e => e.event) || []
        };
        
        console.log('📊 Swap Result:', result);
        return result;
    }
    
    // Helper function to get token balances
    async getTokenBalances(symbols) {
        const balances = {};
        
        for (const symbol of symbols) {
            const token = CONFIG.TOKENS[symbol];
            
            if (token.isNative) {
                const balance = await this.provider.getBalance(this.userAddress);
                balances[symbol] = ethers.utils.formatEther(balance);
            } else {
                const contract = this.contracts[`${symbol.toLowerCase()}Token`];
                const balance = await contract.balanceOf(this.userAddress);
                balances[symbol] = ethers.utils.formatUnits(balance, token.decimals);
            }
        }
        
        return balances;
    }
    
    // Test add liquidity functionality
    async testAddLiquidity() {
        console.log('🏊 Testing Add Liquidity...');
        
        // For testing purposes, we'll simulate the add liquidity process
        // In a real implementation, this would interact with Uniswap V3 position manager
        
        const result = {
            message: 'Add liquidity test simulated - requires Uniswap V3 position manager integration',
            tokenPair: 'ETH/UBA',
            feeTier: '0.3%',
            status: 'SIMULATED'
        };
        
        console.log('📊 Add Liquidity Result:', result);
        return result;
    }
    
    // Test remove liquidity functionality
    async testRemoveLiquidity() {
        console.log('🏊 Testing Remove Liquidity...');
        
        const result = {
            message: 'Remove liquidity test simulated - requires existing position',
            status: 'SIMULATED'
        };
        
        console.log('📊 Remove Liquidity Result:', result);
        return result;
    }
    
    // Test swap and forward functionality
    async testSwapAndForward() {
        console.log('🔄➡️ Testing Swap & Forward...');
        
        // Test forwarding to a different address (using same address for testing)
        const amount = this.testAmounts.small;
        const result = await this.testSwap('ETH', 'UBA', amount);
        
        result.forwardedTo = this.userAddress;
        result.message = 'Swap & Forward completed successfully';
        
        return result;
    }
    
    // Test fee processing functionality
    async testFeeProcessing() {
        console.log('💰 Testing Fee Processing...');
        
        try {
            // Check collected fees
            const ethFees = await this.contracts.feeDistributor.collectedFees(ethers.constants.AddressZero);
            const ubaFees = await this.contracts.feeDistributor.collectedFees(CONFIG.CONTRACTS.UBA_TOKEN);
            
            const result = {
                ethFeesCollected: ethers.utils.formatEther(ethFees),
                ubaFeesCollected: ethers.utils.formatEther(ubaFees),
                message: 'Fee processing check completed'
            };
            
            console.log('📊 Fee Processing Result:', result);
            return result;
            
        } catch (error) {
            throw new Error(`Fee processing test failed: ${error.message}`);
        }
    }
    
    // Test custom token import functionality
    async testCustomTokenImport() {
        console.log('🪙 Testing Custom Token Import...');
        
        // Test with a known token address (WETH)
        const testAddress = CONFIG.TOKENS.WETH.address;
        
        try {
            const tokenContract = new ethers.Contract(
                testAddress,
                CONTRACT_ABIS.ERC20,
                this.provider
            );
            
            const [symbol, name, decimals] = await Promise.all([
                tokenContract.symbol(),
                tokenContract.name(),
                tokenContract.decimals()
            ]);
            
            const result = {
                address: testAddress,
                symbol,
                name,
                decimals: decimals.toString(),
                message: 'Custom token import test successful'
            };
            
            console.log('📊 Custom Token Import Result:', result);
            return result;
            
        } catch (error) {
            throw new Error(`Custom token import test failed: ${error.message}`);
        }
    }
    
    // Print test summary
    printTestSummary() {
        console.log('\n📋 TEST SUMMARY');
        console.log('================');
        
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Total: ${this.testResults.length}`);
        
        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => r.status === 'FAILED')
                .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
        }
        
        console.log('\n🎉 Testing completed!');
    }
}

// Global tester instance
window.dexTester = new DEXTester();

// Utility functions for manual testing
window.testDEX = async () => {
    const initialized = await window.dexTester.initialize();
    if (initialized) {
        await window.dexTester.runAllTests();
    }
};

window.testSwap = async (tokenIn, tokenOut, amount = "0.01") => {
    const initialized = await window.dexTester.initialize();
    if (initialized) {
        const amountWei = ethers.utils.parseEther(amount);
        return await window.dexTester.testSwap(tokenIn, tokenOut, amountWei);
    }
};

window.testBalances = async () => {
    const initialized = await window.dexTester.initialize();
    if (initialized) {
        return await window.dexTester.testBalanceChecks();
    }
};

console.log('🧪 DEX Testing Script Loaded!');
console.log('📝 Available commands:');
console.log('  - testDEX() - Run all tests');
console.log('  - testSwap(tokenIn, tokenOut, amount) - Test specific swap');
console.log('  - testBalances() - Check all token balances');
