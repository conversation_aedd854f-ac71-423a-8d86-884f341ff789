<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 UbaSwap DEX - Feature Testing</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1421;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #ff007a, #ff6b9d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(255, 0, 122, 0.2);
        }

        .test-card h3 {
            color: #ff007a;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }

        .test-card p {
            color: #8b949e;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .test-btn {
            background: linear-gradient(135deg, #ff007a, #ff6b9d);
            border: none;
            color: #ffffff;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 122, 0.3);
        }

        .test-btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .connect-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: #ffffff;
            padding: 1rem 2rem;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.125rem;
            margin-bottom: 2rem;
        }

        .status {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            border-left: 4px solid #ff007a;
        }

        .console {
            background: #000000;
            border-radius: 12px;
            padding: 1.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .console-header {
            color: #ff007a;
            font-weight: bold;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 0.5rem;
        }

        .input-group {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .input-group input, .input-group select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.5rem;
            color: #ffffff;
            flex: 1;
        }

        .token-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .token-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            transition: all 0.2s;
        }

        .token-card:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .token-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .token-symbol {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .token-balance {
            font-size: 0.875rem;
            color: #8b949e;
        }

        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 UbaSwap DEX Testing</h1>
            <p>Comprehensive testing interface for all DEX features</p>
        </div>

        <div class="status" id="status">
            <strong>Status:</strong> <span id="statusText">Please connect your wallet to begin testing</span>
        </div>

        <button id="connectBtn" class="connect-btn" onclick="connectWallet()">
            🔗 Connect Wallet
        </button>

        <div class="token-list" id="tokenList">
            <!-- Token balances will be populated here -->
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🔄 Swap Testing</h3>
                <p>Test token swaps between all supported pairs</p>
                <div class="input-group">
                    <select id="tokenIn">
                        <option value="ETH">ETH</option>
                        <option value="UBA">UBA</option>
                        <option value="WETH">WETH</option>
                        <option value="USDC">USDC</option>
                        <option value="USDT">USDT</option>
                        <option value="DAI">DAI</option>
                    </select>
                    <select id="tokenOut">
                        <option value="UBA">UBA</option>
                        <option value="ETH">ETH</option>
                        <option value="WETH">WETH</option>
                        <option value="USDC">USDC</option>
                        <option value="USDT">USDT</option>
                        <option value="DAI">DAI</option>
                    </select>
                    <input type="number" id="swapAmount" placeholder="0.01" step="0.001" min="0">
                </div>
                <button class="test-btn" onclick="testSingleSwap()" disabled>Test Swap</button>
                <button class="test-btn" onclick="testAllSwaps()" disabled>Test All Swaps</button>
            </div>

            <div class="test-card">
                <h3>🏊 Liquidity Testing</h3>
                <p>Test adding and removing liquidity</p>
                <button class="test-btn" onclick="testAddLiquidity()" disabled>Test Add Liquidity</button>
                <button class="test-btn" onclick="testRemoveLiquidity()" disabled>Test Remove Liquidity</button>
            </div>

            <div class="test-card">
                <h3>🔄➡️ Swap & Forward</h3>
                <p>Test swap and forward functionality</p>
                <div class="input-group">
                    <input type="text" id="forwardAddress" placeholder="Recipient address">
                </div>
                <button class="test-btn" onclick="testSwapForward()" disabled>Test Swap & Forward</button>
            </div>

            <div class="test-card">
                <h3>💰 Fee Processing</h3>
                <p>Test fee collection and distribution</p>
                <button class="test-btn" onclick="testFeeProcessing()" disabled>Check Fees</button>
                <button class="test-btn" onclick="processFees()" disabled>Process Fees</button>
            </div>

            <div class="test-card">
                <h3>🔐 Approvals</h3>
                <p>Test and manage token approvals</p>
                <button class="test-btn" onclick="checkApprovals()" disabled>Check Approvals</button>
                <button class="test-btn" onclick="approveAllTokens()" disabled>Approve All</button>
            </div>

            <div class="test-card">
                <h3>🪙 Custom Tokens</h3>
                <p>Test custom token import</p>
                <div class="input-group">
                    <input type="text" id="customTokenAddress" placeholder="Token contract address">
                </div>
                <button class="test-btn" onclick="testCustomToken()" disabled>Import Token</button>
            </div>
        </div>

        <div class="console" id="console">
            <div class="console-header">🖥️ Console Output</div>
            <div id="consoleContent">Ready for testing...</div>
        </div>

        <div style="margin-top: 2rem; text-align: center;">
            <button class="test-btn" onclick="runAllTests()" disabled style="max-width: 300px; margin: 0 auto;">
                🚀 Run All Tests
            </button>
        </div>
    </div>

    <!-- Include all necessary scripts -->
    <script src="frontend/config.js"></script>
    <script src="frontend/contracts.js"></script>
    <script src="test-dex-features.js"></script>
    
    <script>
        let isConnected = false;
        let userAddress = null;

        // Console logging
        function logToConsole(message, type = 'info') {
            const console = document.getElementById('consoleContent');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            console.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }

        // Override console methods to show in UI
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole(args.join(' '), 'warning');
        };

        // Connect wallet
        async function connectWallet() {
            try {
                if (!window.ethereum) {
                    throw new Error('MetaMask not detected');
                }

                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                userAddress = accounts[0];
                isConnected = true;

                document.getElementById('statusText').textContent = `Connected: ${userAddress}`;
                document.getElementById('connectBtn').textContent = '✅ Wallet Connected';
                document.getElementById('connectBtn').disabled = true;

                // Enable all test buttons
                const testButtons = document.querySelectorAll('.test-btn');
                testButtons.forEach(btn => btn.disabled = false);

                // Initialize tester
                await window.dexTester.initialize();
                
                // Load token balances
                await updateTokenBalances();

                console.log('✅ Wallet connected successfully!');

            } catch (error) {
                console.error('❌ Failed to connect wallet:', error.message);
            }
        }

        // Update token balances display
        async function updateTokenBalances() {
            if (!isConnected) return;

            try {
                const balances = await window.dexTester.testBalanceChecks();
                const tokenList = document.getElementById('tokenList');
                
                tokenList.innerHTML = '';
                
                for (const [symbol, balance] of Object.entries(balances)) {
                    const token = CONFIG.TOKENS[symbol];
                    if (token) {
                        const tokenCard = document.createElement('div');
                        tokenCard.className = 'token-card';
                        tokenCard.innerHTML = `
                            <div class="token-icon">${token.icon}</div>
                            <div class="token-symbol">${symbol}</div>
                            <div class="token-balance">${parseFloat(balance).toFixed(4)}</div>
                        `;
                        tokenList.appendChild(tokenCard);
                    }
                }
            } catch (error) {
                console.error('Failed to update balances:', error.message);
            }
        }

        // Test functions
        async function testSingleSwap() {
            const tokenIn = document.getElementById('tokenIn').value;
            const tokenOut = document.getElementById('tokenOut').value;
            const amount = document.getElementById('swapAmount').value || '0.01';
            
            try {
                await window.testSwap(tokenIn, tokenOut, amount);
                await updateTokenBalances();
            } catch (error) {
                console.error('Swap test failed:', error.message);
            }
        }

        async function testAllSwaps() {
            const tokens = ['ETH', 'UBA', 'WETH'];
            const amount = '0.01';
            
            for (let i = 0; i < tokens.length; i++) {
                for (let j = 0; j < tokens.length; j++) {
                    if (i !== j) {
                        try {
                            console.log(`Testing ${tokens[i]} → ${tokens[j]}`);
                            await window.testSwap(tokens[i], tokens[j], amount);
                            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between swaps
                        } catch (error) {
                            console.error(`Failed ${tokens[i]} → ${tokens[j]}:`, error.message);
                        }
                    }
                }
            }
            await updateTokenBalances();
        }

        async function testAddLiquidity() {
            try {
                await window.dexTester.testAddLiquidity();
            } catch (error) {
                console.error('Add liquidity test failed:', error.message);
            }
        }

        async function testRemoveLiquidity() {
            try {
                await window.dexTester.testRemoveLiquidity();
            } catch (error) {
                console.error('Remove liquidity test failed:', error.message);
            }
        }

        async function testSwapForward() {
            const address = document.getElementById('forwardAddress').value || userAddress;
            try {
                await window.dexTester.testSwapAndForward();
                console.log(`✅ Swap & Forward test completed to ${address}`);
            } catch (error) {
                console.error('Swap & Forward test failed:', error.message);
            }
        }

        async function testFeeProcessing() {
            try {
                await window.dexTester.testFeeProcessing();
            } catch (error) {
                console.error('Fee processing test failed:', error.message);
            }
        }

        async function processFees() {
            console.log('⚠️ Fee processing requires admin privileges');
        }

        async function checkApprovals() {
            try {
                await window.dexTester.testTokenApprovals();
            } catch (error) {
                console.error('Approval check failed:', error.message);
            }
        }

        async function approveAllTokens() {
            console.log('🔐 Approving all tokens...');
            // Implementation would approve all tokens for spending
        }

        async function testCustomToken() {
            const address = document.getElementById('customTokenAddress').value;
            if (!address) {
                console.error('Please enter a token address');
                return;
            }
            
            try {
                await window.dexTester.testCustomTokenImport();
            } catch (error) {
                console.error('Custom token test failed:', error.message);
            }
        }

        async function runAllTests() {
            try {
                await window.testDEX();
                await updateTokenBalances();
            } catch (error) {
                console.error('Full test suite failed:', error.message);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 DEX Testing Interface Ready!');
            console.log('📝 Connect your wallet to begin testing all features');
        });
    </script>
</body>
</html>
