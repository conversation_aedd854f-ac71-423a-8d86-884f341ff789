import React, { useState } from 'react';
import {
  useBalance,
  useContractWrite,
  usePrepareContractWrite,
  useWaitForTransaction,
  useContractRead
} from 'wagmi';
import { parseEther, parseUnits, formatEther } from 'viem';
import { CONTRACT_ADDRESSES, TOKENS, CONTRACT_ABIS, FEE_TIERS } from '../../config/contracts';
import TokenSelector from '../common/TokenSelector';
import './LiquidityPage.css';

const LiquidityPage = ({ isConnected, address, isCorrectNetwork, showToast }) => {
  const [activeTab, setActiveTab] = useState('add');
  const [selectedPair, setSelectedPair] = useState(['ETH', 'UBA']);
  const [feeTier, setFeeTier] = useState(3000); // 0.3%
  const [amount0, setAmount0] = useState('');
  const [amount1, setAmount1] = useState('');
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [showTokenSelector, setShowTokenSelector] = useState(false);
  const [tokenSelectorType, setTokenSelectorType] = useState('tokenA'); // 'tokenA' or 'tokenB'

  // Balance hooks
  const { data: ethBalance } = useBalance({
    address,
    enabled: isConnected && isCorrectNetwork,
  });

  const { data: ubaBalance } = useBalance({
    address,
    token: CONTRACT_ADDRESSES.UBA_TOKEN,
    enabled: isConnected && isCorrectNetwork,
  });

  const getCurrentBalance = (token) => {
    if (token === 'ETH') {
      return ethBalance ? parseFloat(ethBalance.formatted) : 0;
    } else {
      return ubaBalance ? parseFloat(ubaBalance.formatted) : 0;
    }
  };

  const handleMaxAmount = (index) => {
    const token = selectedPair[index];
    const balance = getCurrentBalance(token);
    const maxAmount = token === 'ETH' ? Math.max(0, balance - 0.01) : balance;

    if (index === 0) {
      setAmount0(maxAmount.toString());
      // Calculate proportional amount for token1
      if (selectedPair[0] === 'ETH' && selectedPair[1] === 'UBA') {
        setAmount1((maxAmount * 1000).toString());
      }
    } else {
      setAmount1(maxAmount.toString());
      // Calculate proportional amount for token0
      if (selectedPair[0] === 'ETH' && selectedPair[1] === 'UBA') {
        setAmount0((maxAmount / 1000).toString());
      }
    }
  };

  // Add liquidity preparation
  const { config: addLiquidityConfig } = usePrepareContractWrite({
    address: CONTRACT_ADDRESSES.LIQUIDITY_MANAGER,
    abi: [
      'function addLiquidity(address token0, address token1, uint24 fee, int24 tickLower, int24 tickUpper, uint256 amount0Desired, uint256 amount1Desired, uint256 amount0Min, uint256 amount1Min, uint256 deadline) external payable returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)'
    ],
    functionName: 'addLiquidity',
    args: amount0 && amount1 ? [
      selectedPair[0] === 'ETH' ? '******************************************' : TOKENS[selectedPair[0]]?.address,
      selectedPair[1] === 'ETH' ? '******************************************' : TOKENS[selectedPair[1]]?.address,
      feeTier,
      -887220, // tickLower (simplified)
      887220,  // tickUpper (simplified)
      selectedPair[0] === 'ETH' ? parseEther(amount0) : parseUnits(amount0, TOKENS[selectedPair[0]]?.decimals || 18),
      selectedPair[1] === 'ETH' ? parseEther(amount1) : parseUnits(amount1, TOKENS[selectedPair[1]]?.decimals || 18),
      selectedPair[0] === 'ETH' ? parseEther((parseFloat(amount0) * 0.99).toString()) : parseUnits((parseFloat(amount0) * 0.99).toString(), TOKENS[selectedPair[0]]?.decimals || 18),
      selectedPair[1] === 'ETH' ? parseEther((parseFloat(amount1) * 0.99).toString()) : parseUnits((parseFloat(amount1) * 0.99).toString(), TOKENS[selectedPair[1]]?.decimals || 18),
      Math.floor(Date.now() / 1000) + 1200 // 20 minutes deadline
    ] : undefined,
    value: selectedPair[0] === 'ETH' ? parseEther(amount0 || '0') : selectedPair[1] === 'ETH' ? parseEther(amount1 || '0') : undefined,
    enabled: Boolean(amount0 && amount1 && isConnected && isCorrectNetwork),
  });

  const {
    data: addLiquidityData,
    write: addLiquidity,
    isLoading: isAddingLiquidity
  } = useContractWrite(addLiquidityConfig);

  const { isLoading: isWaitingForAddLiquidity } = useWaitForTransaction({
    hash: addLiquidityData?.hash,
    onSuccess: () => {
      showToast({
        type: 'success',
        title: 'Liquidity Added!',
        message: 'Your liquidity has been successfully added to the pool',
      });
      setAmount0('');
      setAmount1('');
    },
    onError: (error) => {
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        message: error.message || 'Failed to add liquidity',
      });
    },
  });

  const handleAddLiquidity = async () => {
    if (!isConnected || !isCorrectNetwork) {
      showToast({
        type: 'error',
        title: 'Wallet Error',
        message: 'Please connect wallet and switch to Sepolia',
      });
      return;
    }

    if (!amount0 || !amount1) {
      showToast({
        type: 'error',
        title: 'Invalid Input',
        message: 'Please enter amounts for both tokens',
      });
      return;
    }

    try {
      addLiquidity?.();
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Transaction Error',
        message: error.message || 'Failed to add liquidity',
      });
    }
  };

  return (
    <div className="liquidity-page">
      <div className="page-header">
        <h1>💧 Liquidity Management</h1>
        <p>Provide liquidity to earn fees from trades</p>
      </div>

      {/* Tabs */}
      <div className="liquidity-tabs">
        {[
          { id: 'add', label: 'Add Liquidity', icon: '➕' },
          { id: 'remove', label: 'Remove Liquidity', icon: '➖' },
          { id: 'positions', label: 'My Positions', icon: '📊' },
        ].map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Add Liquidity Tab */}
      {activeTab === 'add' && (
        <div className="card">
          <h3>Add Liquidity</h3>

          {/* Pair Selection */}
          <div className="pair-selector">
            <div className="token-pair">
              <button className="pair-token">
                <span className="token-icon">{TOKENS[selectedPair[0]].icon}</span>
                <span>{selectedPair[0]}</span>
              </button>
              <span className="pair-separator">+</span>
              <button className="pair-token">
                <span className="token-icon">{TOKENS[selectedPair[1]].icon}</span>
                <span>{selectedPair[1]}</span>
              </button>
            </div>

            {/* Fee Tier */}
            <div className="fee-tier">
              <label>Fee Tier:</label>
              <select value={feeTier} onChange={(e) => setFeeTier(parseInt(e.target.value))}>
                <option value={500}>0.05% (Best for stable pairs)</option>
                <option value={3000}>0.3% (Most common)</option>
                <option value={10000}>1% (Exotic pairs)</option>
              </select>
            </div>
          </div>

          {/* Amount Inputs */}
          <div className="liquidity-amounts">
            <div className="amount-input-group">
              <label>Amount {selectedPair[0]}</label>
              <div className="input-wrapper">
                <input
                  type="number"
                  placeholder="0.0"
                  value={amount0}
                  onChange={(e) => setAmount0(e.target.value)}
                  className="input-field"
                />
                <button
                  className="max-btn"
                  onClick={() => handleMaxAmount(0)}
                >
                  MAX
                </button>
              </div>
              <div className="balance-info">
                Balance: {getCurrentBalance(selectedPair[0]).toFixed(4)} {selectedPair[0]}
              </div>
            </div>

            <div className="amount-input-group">
              <label>Amount {selectedPair[1]}</label>
              <div className="input-wrapper">
                <input
                  type="number"
                  placeholder="0.0"
                  value={amount1}
                  onChange={(e) => setAmount1(e.target.value)}
                  className="input-field"
                />
                <button
                  className="max-btn"
                  onClick={() => handleMaxAmount(1)}
                >
                  MAX
                </button>
              </div>
              <div className="balance-info">
                Balance: {getCurrentBalance(selectedPair[1]).toFixed(4)} {selectedPair[1]}
              </div>
            </div>
          </div>

          {/* Price Range */}
          <div className="price-range">
            <h4>Price Range</h4>
            <div className="range-presets">
              <button onClick={() => setPriceRange({ min: '0', max: '∞' })}>
                Full Range
              </button>
              <button onClick={() => setPriceRange({ min: '800', max: '1200' })}>
                Safe Range
              </button>
              <button onClick={() => setPriceRange({ min: '950', max: '1050' })}>
                Narrow Range
              </button>
            </div>
            <div className="range-inputs">
              <div className="range-input">
                <label>Min Price</label>
                <input
                  type="text"
                  placeholder="0"
                  value={priceRange.min}
                  onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                />
              </div>
              <div className="range-input">
                <label>Max Price</label>
                <input
                  type="text"
                  placeholder="∞"
                  value={priceRange.max}
                  onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                />
              </div>
            </div>
          </div>

          {/* Add Liquidity Button */}
          <button
            className="btn-primary w-full"
            onClick={handleAddLiquidity}
            disabled={!amount0 || !amount1 || isAddingLiquidity || isWaitingForAddLiquidity}
          >
            {isAddingLiquidity || isWaitingForAddLiquidity ? (
              <>
                <span className="spinner"></span>
                {isAddingLiquidity ? 'Preparing...' : 'Adding Liquidity...'}
              </>
            ) : (
              'Add Liquidity'
            )}
          </button>
        </div>
      )}

      {/* Remove Liquidity Tab */}
      {activeTab === 'remove' && (
        <div className="card">
          <h3>Remove Liquidity</h3>
          <div className="status info">
            🚧 Remove liquidity functionality coming soon with Uniswap V3 integration
          </div>
        </div>
      )}

      {/* My Positions Tab */}
      {activeTab === 'positions' && (
        <div className="card">
          <h3>My Positions</h3>
          {isConnected && isCorrectNetwork ? (
            <div className="status info">
              📊 No liquidity positions found. Add liquidity to start earning fees!
            </div>
          ) : (
            <div className="status warning">
              Please connect your wallet to view positions
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LiquidityPage;
