// Contract addresses on Sepolia testnet
export const CONTRACT_ADDRESSES = {
  UBA_TOKEN: '******************************************',
  SWAP_AND_FORWARD: '******************************************',
  FEE_DISTRIBUTOR: '******************************************',
  WETH: '******************************************',
  UNISWAP_ROUTER: '******************************************',
  UNISWAP_QUOTER: '******************************************',
};

// Token configurations
export const TOKENS = {
  ETH: {
    address: '******************************************',
    symbol: 'ETH',
    name: 'Ether<PERSON>',
    decimals: 18,
    icon: 'Ξ',
    color: '#627eea',
    isNative: true,
  },
  UBA: {
    address: CONTRACT_ADDRESSES.UBA_TOKEN,
    symbol: 'UBA',
    name: 'UBA Token',
    decimals: 18,
    icon: '🦄',
    color: '#ff007a',
    isNative: false,
  },
  WETH: {
    address: '******************************************',
    symbol: 'WETH',
    name: 'Wrapped Ether',
    decimals: 18,
    icon: '🔄',
    color: '#627eea',
    isNative: false,
  },
  USDC: {
    address: '******************************************',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    icon: '💵',
    color: '#2775ca',
    isNative: false,
  },
  USDT: {
    address: '******************************************',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    icon: '💰',
    color: '#26a17b',
    isNative: false,
  },
  DAI: {
    address: '******************************************',
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    decimals: 18,
    icon: '🟡',
    color: '#f5ac37',
    isNative: false,
  },
};

// Protocol configuration
export const PROTOCOL_CONFIG = {
  FEE_BPS: 50, // 0.5%
  MAX_FEE_BPS: 1000, // 10%
  BASIS_POINTS: 10000,
  DEFAULT_SLIPPAGE: 0.5, // 0.5%
  DEFAULT_DEADLINE: 20, // 20 minutes
  MIN_PROCESS_AMOUNT: '1000000000000000000', // 1 ETH in wei
};

// Uniswap V3 fee tiers
export const FEE_TIERS = {
  LOW: 500,    // 0.05%
  MEDIUM: 3000, // 0.3%
  HIGH: 10000,  // 1%
};

// Contract ABIs
export const CONTRACT_ABIS = {
  ERC20: [
    'function balanceOf(address owner) view returns (uint256)',
    'function transfer(address to, uint256 amount) returns (bool)',
    'function approve(address spender, uint256 amount) returns (bool)',
    'function allowance(address owner, address spender) view returns (uint256)',
    'function symbol() view returns (string)',
    'function name() view returns (string)',
    'function decimals() view returns (uint8)',
    'function totalSupply() view returns (uint256)',
    'event Transfer(address indexed from, address indexed to, uint256 value)',
    'event Approval(address indexed owner, address indexed spender, uint256 value)',
  ],

  UBA_TOKEN: [
    'function balanceOf(address owner) view returns (uint256)',
    'function transfer(address to, uint256 amount) returns (bool)',
    'function approve(address spender, uint256 amount) returns (bool)',
    'function allowance(address owner, address spender) view returns (uint256)',
    'function symbol() view returns (string)',
    'function name() view returns (string)',
    'function decimals() view returns (uint8)',
    'function totalSupply() view returns (uint256)',
    'function mint(address to, uint256 amount)',
    'function burnForFees(uint256 amount)',
    'function setFeeDistributorAddress(address _newFeeDistributor)',
    'function feeDistributorContractAddress() view returns (address)',
    'function MAX_SUPPLY() view returns (uint256)',
    'function INITIAL_SUPPLY() view returns (uint256)',
    'event TokensMinted(address indexed to, uint256 amount)',
    'event TokensBurnedForFees(uint256 amount, address indexed burner)',
  ],

  SWAP_AND_FORWARD: [
    'function swapAndForwardSingleHop(address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address recipient, uint24 fee, uint256 deadline) payable returns (uint256)',
    'function protocolFeeBps() view returns (uint256)',
    'function updateProtocolFee(uint256 newFeeBps)',
    'function emergencyRecoverToken(address token, uint256 amount)',
    'function emergencyRecoverETH()',
    'function owner() view returns (address)',
    'event SwapAndForwardExecuted(address indexed user, address indexed tokenIn, address indexed tokenOut, uint256 amountIn, uint256 amountOut, uint256 protocolFee, address recipient)',
    'event FeesCollected(address indexed token, uint256 amount, address indexed feeDistributor)',
    'event ProtocolFeeUpdated(uint256 oldFee, uint256 newFee)',
  ],

  FEE_DISTRIBUTOR: [
    'function collectFees(address token, uint256 amount)',
    'function processFees(address token, uint24 fee)',
    'function updateMinimumProcessAmount(uint256 newAmount)',
    'function emergencyRecoverToken(address token, uint256 amount)',
    'function getCollectedFees(address token) view returns (uint256)',
    'function minimumProcessAmount() view returns (uint256)',
    'function ubaToken() view returns (address)',
    'function uniswapRouter() view returns (address)',
    'function WETH() view returns (address)',
    'function owner() view returns (address)',
    'event FeesProcessed(address indexed token, uint256 amount, uint256 ubaAmount)',
    'event MinimumProcessAmountUpdated(uint256 oldAmount, uint256 newAmount)',
    'event FeesCollected(address indexed token, uint256 amount)',
  ],

  UNISWAP_ROUTER: [
    'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96)) payable returns (uint256 amountOut)',
    'function exactInput((bytes path, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum)) payable returns (uint256 amountOut)',
  ],

  UNISWAP_QUOTER: [
    'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) returns (uint256 amountOut, uint160 sqrtPriceX96After, uint32 initializedTicksCrossed, uint256 gasEstimate)',
  ],

  WETH: [
    'function deposit() payable',
    'function withdraw(uint256 amount)',
    'function balanceOf(address owner) view returns (uint256)',
    'function transfer(address to, uint256 amount) returns (bool)',
    'function approve(address spender, uint256 amount) returns (bool)',
    'function allowance(address owner, address spender) view returns (uint256)',
  ],
};

// External links
export const EXTERNAL_LINKS = {
  ETHERSCAN_BASE: 'https://sepolia.etherscan.io',
  FAUCET: 'https://sepoliafaucet.com',
  DOCS: 'https://docs.uniswap.org',
  GITHUB: 'https://github.com/ubaswap',
  TWITTER: 'https://twitter.com/ubaswap',
  DISCORD: 'https://discord.gg/ubaswap',
};

// Error and success messages
export const MESSAGES = {
  ERRORS: {
    WALLET_NOT_CONNECTED: 'Please connect your wallet first',
    WRONG_NETWORK: 'Please switch to Sepolia testnet',
    INSUFFICIENT_BALANCE: 'Insufficient balance',
    INVALID_AMOUNT: 'Invalid amount',
    INVALID_ADDRESS: 'Invalid address',
    TRANSACTION_FAILED: 'Transaction failed',
    USER_REJECTED: 'Transaction rejected by user',
    SLIPPAGE_TOO_HIGH: 'Slippage tolerance too high',
    NETWORK_ERROR: 'Network error, please try again',
    CONTRACT_ERROR: 'Contract interaction failed',
  },
  SUCCESS: {
    WALLET_CONNECTED: 'Wallet connected successfully!',
    TRANSACTION_SENT: 'Transaction sent successfully!',
    SWAP_COMPLETED: 'Swap completed successfully!',
    LIQUIDITY_ADDED: 'Liquidity added successfully!',
    LIQUIDITY_REMOVED: 'Liquidity removed successfully!',
    FEES_CLAIMED: 'Fees claimed successfully!',
    SETTINGS_UPDATED: 'Settings updated successfully!',
  },
  INFO: {
    PREPARING_TRANSACTION: 'Preparing transaction...',
    WAITING_CONFIRMATION: 'Waiting for confirmation...',
    APPROVING_TOKEN: 'Approving token spend...',
    PROCESSING_FEES: 'Processing fees...',
    LOADING_DATA: 'Loading data...',
  },
};

// UI configuration
export const UI_CONFIG = {
  REFRESH_INTERVAL: 30000, // 30 seconds
  PRICE_UPDATE_INTERVAL: 5000, // 5 seconds
  BALANCE_DECIMALS: 4,
  AMOUNT_DECIMALS: 6,
  ANIMATION_DURATION: 200, // milliseconds
  TOAST_DURATION: 5000, // 5 seconds
};

// Default export with all configs
export default {
  CONTRACT_ADDRESSES,
  TOKENS,
  PROTOCOL_CONFIG,
  FEE_TIERS,
  CONTRACT_ABIS,
  EXTERNAL_LINKS,
  MESSAGES,
  UI_CONFIG,
};
