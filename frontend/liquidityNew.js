/**
 * 🏊 Enhanced Liquidity Management
 * Handles add/remove liquidity operations with deployed LiquidityManager contract
 */

class EnhancedLiquidityManager {
    constructor() {
        this.contract = null;
        this.provider = null;
        this.signer = null;
        this.userPositions = [];
        
        // Contract address from deployment
        this.contractAddress = '******************************************';
        
        // Fee tiers (in basis points)
        this.feeTiers = {
            LOW: 500,    // 0.05%
            MEDIUM: 3000, // 0.3%
            HIGH: 10000   // 1%
        };

        this.selectedTokenA = CONFIG.TOKENS.ETH;
        this.selectedTokenB = CONFIG.TOKENS.UBA;
    }

    // Initialize liquidity manager
    async initialize() {
        try {
            if (!window.ethereum) {
                throw new Error('MetaMask not detected');
            }

            this.provider = new ethers.providers.Web3Provider(window.ethereum);
            this.signer = this.provider.getSigner();
            
            // Initialize contract with ABI
            const liquidityManagerABI = [
                "function addLiquidity(address token0, address token1, uint24 fee, int24 tickLower, int24 tickUpper, uint256 amount0Desired, uint256 amount1Desired, uint256 amount0Min, uint256 amount1Min, uint256 deadline) external payable returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)",
                "function removeLiquidity(uint256 tokenId, uint128 liquidity, uint256 amount0Min, uint256 amount1Min, uint256 deadline) external returns (uint256 amount0, uint256 amount1)",
                "function collectFees(uint256 tokenId) external returns (uint256 amount0, uint256 amount1)",
                "function getUserPositions(address user) external view returns (uint256[] memory)",
                "function positions(uint256 tokenId) external view returns (uint256, address, address, uint24, int24, int24, uint128, address)",
                "event LiquidityAdded(address indexed user, uint256 indexed tokenId, address token0, address token1, uint24 fee, uint128 liquidity, uint256 amount0, uint256 amount1)"
            ];
            
            this.contract = new ethers.Contract(
                this.contractAddress,
                liquidityManagerABI,
                this.signer
            );

            console.log('✅ Enhanced LiquidityManager initialized');
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize Enhanced LiquidityManager:', error);
            return false;
        }
    }

    // Add liquidity to a pool
    async addLiquidity() {
        try {
            if (!this.contract) {
                await this.initialize();
            }

            // Get input values
            const amountA = document.getElementById('tokenAAmount').value;
            const amountB = document.getElementById('tokenBAmount').value;
            const minPrice = document.getElementById('minPrice').value || '0.1';
            const maxPrice = document.getElementById('maxPrice').value || '10';

            if (!amountA || !amountB) {
                window.uiManager.showStatus('Please enter amounts for both tokens', 'error');
                return;
            }

            console.log('🏊 Adding liquidity...');
            console.log(`Token A: ${this.selectedTokenA.symbol} - ${amountA}`);
            console.log(`Token B: ${this.selectedTokenB.symbol} - ${amountB}`);

            // Show loading
            window.uiManager.showLoading('Adding liquidity...');

            // Convert amounts to wei
            const amountAWei = ethers.utils.parseUnits(amountA.toString(), this.selectedTokenA.decimals);
            const amountBWei = ethers.utils.parseUnits(amountB.toString(), this.selectedTokenB.decimals);

            // Calculate tick range from prices (simplified)
            const { tickLower, tickUpper } = this.calculateTickRange(parseFloat(minPrice), parseFloat(maxPrice));

            // Prepare transaction parameters
            const deadline = Math.floor(Date.now() / 1000) + (20 * 60); // 20 minutes
            const fee = this.feeTiers.MEDIUM; // 0.3%

            // Handle ETH/WETH conversion
            let token0Address = this.selectedTokenA.address;
            let token1Address = this.selectedTokenB.address;
            let value = 0;

            if (this.selectedTokenA.isNative) {
                token0Address = ethers.constants.AddressZero;
                value = amountAWei;
            } else if (this.selectedTokenB.isNative) {
                token1Address = ethers.constants.AddressZero;
                value = amountBWei;
            }

            // Approve tokens if needed
            await this.approveTokensIfNeeded(this.selectedTokenA, this.selectedTokenB, amountAWei, amountBWei);

            // Calculate minimum amounts (with 1% slippage)
            const amount0Min = amountAWei.mul(99).div(100);
            const amount1Min = amountBWei.mul(99).div(100);

            // Add liquidity
            const tx = await this.contract.addLiquidity(
                token0Address,
                token1Address,
                fee,
                tickLower,
                tickUpper,
                amountAWei,
                amountBWei,
                amount0Min,
                amount1Min,
                deadline,
                { value: value }
            );

            console.log('⏳ Transaction submitted:', tx.hash);
            window.uiManager.showStatus(`Transaction submitted: ${tx.hash}`, 'info');

            // Wait for confirmation
            const receipt = await tx.wait();
            console.log('✅ Liquidity added successfully!');

            window.uiManager.hideLoading();
            window.uiManager.showStatus('Liquidity added successfully!', 'success');

            // Clear inputs
            document.getElementById('tokenAAmount').value = '';
            document.getElementById('tokenBAmount').value = '';

            // Refresh positions
            await this.loadUserPositions();

            return receipt;

        } catch (error) {
            window.uiManager.hideLoading();
            console.error('❌ Add liquidity failed:', error);
            
            let errorMessage = 'Failed to add liquidity';
            if (error.message.includes('insufficient funds')) {
                errorMessage = 'Insufficient funds for transaction';
            } else if (error.message.includes('user rejected')) {
                errorMessage = 'Transaction rejected by user';
            } else if (error.message.includes('execution reverted')) {
                errorMessage = 'Transaction failed - check token balances and approvals';
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
            throw error;
        }
    }

    // Load user positions
    async loadUserPositions() {
        try {
            if (!this.contract || !window.walletManager.userAddress) {
                return [];
            }

            console.log('📋 Loading user positions...');

            const positionIds = await this.contract.getUserPositions(window.walletManager.userAddress);
            this.userPositions = [];

            for (const tokenId of positionIds) {
                try {
                    const position = await this.contract.positions(tokenId);
                    this.userPositions.push({
                        tokenId: tokenId.toString(),
                        token0: position[1],
                        token1: position[2],
                        fee: position[3],
                        tickLower: position[4],
                        tickUpper: position[5],
                        liquidity: position[6].toString(),
                        owner: position[7]
                    });
                } catch (error) {
                    console.error(`Error loading position ${tokenId}:`, error);
                }
            }

            console.log(`✅ Loaded ${this.userPositions.length} positions`);
            this.updatePositionsUI();

            return this.userPositions;

        } catch (error) {
            console.error('❌ Failed to load positions:', error);
            return [];
        }
    }

    // Update positions UI
    updatePositionsUI() {
        const positionsContainer = document.getElementById('userPositions');
        if (!positionsContainer) return;

        if (this.userPositions.length === 0) {
            positionsContainer.innerHTML = `
                <div class="no-positions">
                    <p>No liquidity positions found</p>
                    <p>Add liquidity to start earning fees</p>
                </div>
            `;
            return;
        }

        positionsContainer.innerHTML = this.userPositions.map(position => `
            <div class="position-card" data-token-id="${position.tokenId}">
                <div class="position-header">
                    <h4>Position #${position.tokenId}</h4>
                    <span class="fee-tier">${position.fee / 100}%</span>
                </div>
                
                <div class="position-info">
                    <div class="token-pair">
                        <span>${this.getTokenSymbol(position.token0)}</span>
                        <span>/</span>
                        <span>${this.getTokenSymbol(position.token1)}</span>
                    </div>
                    
                    <div class="liquidity-amount">
                        Liquidity: ${this.formatLiquidity(position.liquidity)}
                    </div>
                </div>
                
                <div class="position-actions">
                    <button class="action-btn small" onclick="window.enhancedLiquidityManager.collectFees('${position.tokenId}')">
                        Collect Fees
                    </button>
                    <button class="action-btn small secondary" onclick="window.enhancedLiquidityManager.removeLiquidity('${position.tokenId}')">
                        Remove
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Helper function to approve tokens if needed
    async approveTokensIfNeeded(tokenA, tokenB, amountA, amountB) {
        const approvals = [];

        if (!tokenA.isNative) {
            const tokenAContract = new ethers.Contract(tokenA.address, CONTRACT_ABIS.ERC20, this.signer);
            const allowanceA = await tokenAContract.allowance(window.walletManager.userAddress, this.contractAddress);
            
            if (allowanceA.lt(amountA)) {
                console.log(`🔐 Approving ${tokenA.symbol}...`);
                window.uiManager.showStatus(`Approving ${tokenA.symbol}...`, 'info');
                const approveTx = await tokenAContract.approve(this.contractAddress, amountA);
                approvals.push(approveTx.wait());
            }
        }

        if (!tokenB.isNative) {
            const tokenBContract = new ethers.Contract(tokenB.address, CONTRACT_ABIS.ERC20, this.signer);
            const allowanceB = await tokenBContract.allowance(window.walletManager.userAddress, this.contractAddress);
            
            if (allowanceB.lt(amountB)) {
                console.log(`🔐 Approving ${tokenB.symbol}...`);
                window.uiManager.showStatus(`Approving ${tokenB.symbol}...`, 'info');
                const approveTx = await tokenBContract.approve(this.contractAddress, amountB);
                approvals.push(approveTx.wait());
            }
        }

        if (approvals.length > 0) {
            console.log('⏳ Waiting for approvals...');
            await Promise.all(approvals);
            console.log('✅ Tokens approved');
        }
    }

    // Calculate tick range from price range (simplified)
    calculateTickRange(minPrice, maxPrice) {
        // Simplified tick calculation for demo
        // In production, use proper tick math from Uniswap V3
        const tickSpacing = 60; // For 0.3% fee tier
        
        // Convert prices to ticks (simplified)
        const tickLower = Math.floor(Math.log(minPrice) / Math.log(1.0001) / tickSpacing) * tickSpacing;
        const tickUpper = Math.ceil(Math.log(maxPrice) / Math.log(1.0001) / tickSpacing) * tickSpacing;
        
        return { tickLower, tickUpper };
    }

    // Collect fees from a position
    async collectFees(tokenId) {
        try {
            if (!this.contract) {
                await this.initialize();
            }

            console.log('💰 Collecting fees...');
            window.uiManager.showLoading('Collecting fees...');

            const tx = await this.contract.collectFees(tokenId);
            console.log('⏳ Transaction submitted:', tx.hash);

            const receipt = await tx.wait();
            console.log('✅ Fees collected successfully!');

            window.uiManager.hideLoading();
            window.uiManager.showStatus('Fees collected successfully!', 'success');

            return receipt;

        } catch (error) {
            window.uiManager.hideLoading();
            console.error('❌ Collect fees failed:', error);
            window.uiManager.showStatus('Failed to collect fees', 'error');
            throw error;
        }
    }

    // Remove liquidity (simplified)
    async removeLiquidity(tokenId) {
        try {
            console.log(`Removing liquidity from position ${tokenId}`);
            window.uiManager.showStatus('Remove liquidity feature coming soon!', 'info');
        } catch (error) {
            console.error('Remove liquidity failed:', error);
            window.uiManager.showStatus('Failed to remove liquidity', 'error');
        }
    }

    // Helper functions
    getTokenSymbol(address) {
        if (address === ethers.constants.AddressZero) return 'ETH';
        const token = Object.values(CONFIG.TOKENS).find(t => 
            t.address.toLowerCase() === address.toLowerCase()
        );
        return token ? token.symbol : 'Unknown';
    }

    formatLiquidity(liquidity) {
        const num = parseFloat(liquidity);
        if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
        if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
        if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
        return num.toFixed(2);
    }

    // Set selected tokens
    setTokenA(token) {
        this.selectedTokenA = token;
        this.updateTokenUI('tokenA', token);
    }

    setTokenB(token) {
        this.selectedTokenB = token;
        this.updateTokenUI('tokenB', token);
    }

    updateTokenUI(tokenType, token) {
        const button = document.querySelector(`[onclick*="${tokenType}"]`);
        if (button) {
            const icon = button.querySelector('.token-icon');
            const symbol = button.querySelector('.token-symbol');
            if (icon) icon.textContent = token.icon;
            if (symbol) symbol.textContent = token.symbol;
        }
    }
}

// Export for use in other scripts
window.EnhancedLiquidityManager = EnhancedLiquidityManager;
