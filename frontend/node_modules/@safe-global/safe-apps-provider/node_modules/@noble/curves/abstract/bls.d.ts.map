{"version": 3, "file": "bls.d.ts", "sourceRoot": "", "sources": ["../src/abstract/bls.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;IAeI;AACJ,sEAAsE;AACtE,OAAO,EAKL,KAAK,KAAK,EACV,KAAK,GAAG,EACR,KAAK,OAAO,EACb,MAAM,aAAa,CAAC;AAErB,OAAO,EAEL,KAAK,SAAS,EACd,KAAK,WAAW,EAChB,KAAK,OAAO,EAEZ,KAAK,YAAY,EACjB,KAAK,UAAU,EAChB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAoC,KAAK,MAAM,EAAE,MAAM,cAAc,CAAC;AAC7E,OAAO,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACrE,OAAO,EAEL,KAAK,cAAc,EACnB,KAAK,eAAe,EACpB,KAAK,eAAe,EACpB,KAAK,aAAa,EACnB,MAAM,kBAAkB,CAAC;AAE1B,KAAK,EAAE,GAAG,MAAM,CAAC;AAKjB,MAAM,MAAM,SAAS,GAAG,gBAAgB,GAAG,UAAU,CAAC;AAEtD,MAAM,MAAM,mBAAmB,CAAC,EAAE,IAAI;IACpC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IACrC,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IAC9C,gCAAgC;IAChC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,EAAE,IAAI;IAC/B,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IACrC,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IAC9C,gCAAgC;IAChC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG,CACrC,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,KACJ;IAAE,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAA;CAAE,CAAC;AACnC,MAAM,MAAM,gBAAgB,GAAG,CAC7B,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,QAAQ,EAAE,wBAAwB,KAC/B,IAAI,CAAC;AACV,MAAM,MAAM,SAAS,GAAG;IACtB,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,GAAG;QACxB,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;QACnC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAC3B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,EAAE,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG;QACzB,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC/B,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACnB,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,OAAO,CAAC;KACf,CAAC;IACF,MAAM,EAAE;QAIN,WAAW,EAAE,MAAM,CAAC;QACpB,SAAS,EAAE,OAAO,CAAC;QACnB,CAAC,EAAE,MAAM,CAAC;QACV,SAAS,EAAE,SAAS,CAAC;KACtB,CAAC;IACF,WAAW,EAAE,OAAO,CAAC;IACrB,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IAEnD,cAAc,CAAC,EAAE,gBAAgB,CAAC;CACnC,CAAC;AAEF,KAAK,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AAC1C,KAAK,UAAU,GAAG,gBAAgB,EAAE,CAAC;AAErC,MAAM,MAAM,OAAO,GAAG;IACpB,cAAc,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACrC,eAAe,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAEtC,eAAe,EAAE,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC;IACzD,OAAO,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,iBAAiB,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;IAC5F,YAAY,EAAE,CACZ,KAAK,EAAE;QAAE,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;QAAC,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,CAAA;KAAE,EAAE,EAC1D,iBAAiB,CAAC,EAAE,OAAO,KACxB,IAAI,CAAC;IAEV,oDAAoD;IACpD,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IAClD,qDAAqD;IACrD,8BAA8B,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IACpE,4CAA4C;IAC5C,IAAI,EAAE;QACJ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KAChG,CAAC;IACF,6CAA6C;IAC7C,kBAAkB,EAAE;QAClB,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KAC9F,CAAC;IACF,8CAA8C;IAC9C,MAAM,EAAE,CACN,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACjC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,+CAA+C;IAC/C,oBAAoB,EAAE,CACpB,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAChC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,WAAW,EAAE,CACX,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,QAAQ,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,EACtC,UAAU,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,EACvC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,2DAA2D;IAC3D,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,2DAA2D;IAC3D,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KACxD,CAAC;IACF,4DAA4D;IAC5D,wBAAwB,EAAE;QACxB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,kDAAkD;IAClD,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IACvC,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACzC,iDAAiD;IACjD,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;IAC/B,kDAAkD;IAClD,cAAc,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,MAAM,EAAE;QACN,WAAW,EAAE,MAAM,CAAC;QACpB,CAAC,EAAE,MAAM,CAAC;QACV,SAAS,EAAE,SAAS,CAAC;QACrB,kBAAkB;QAClB,GAAG,EAAE,MAAM,CAAC;QACZ,kBAAkB;QAClB,GAAG,EAAE,GAAG,CAAC;KACV,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAC5B,EAAE,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;KAC1B,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,OAAO,CAAC;QACd,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpB,CAAC;IACF,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,sBAAsB,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC;KAC/D,CAAC;CACH,CAAC;AAEF,KAAK,QAAQ,GAAG,GAAG,GAAG,UAAU,CAAC;AACjC,MAAM,WAAW,OAAO,CAAC,CAAC,EAAE,CAAC;IAC3B,YAAY,CAAC,UAAU,EAAE,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7E,MAAM,CACJ,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,EACtC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,EACzB,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,GACrC,OAAO,CAAC;IACX,mBAAmB,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACnF,mBAAmB,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACnF,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC/F,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;CAC9B;AAiBD,wBAAgB,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,OAAO,CAsb7C"}