{"version": 3, "file": "curve.js", "sourceRoot": "", "sources": ["../../src/abstract/curve.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,sEAAsE;AACtE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC9D,OAAO,EAAE,KAAK,EAAE,aAAa,EAAe,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAEzF,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AA4BtB,MAAM,UAAU,QAAQ,CAAqB,SAAkB,EAAE,IAAO;IACtE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC1B,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AAChC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CACxB,CAA8B,EAC9B,QAAqB,EACrB,MAAW;IAEX,MAAM,IAAI,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACrE,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACpD,aAAa;IACb,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,SAAS,CAAC,CAAS,EAAE,IAAY;IACxC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;QAChD,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;AACnF,CAAC;AAWD,SAAS,SAAS,CAAC,CAAS,EAAE,UAAkB;IAC9C,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,uCAAuC;IACtF,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,yCAAyC;IAC1E,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;IACpC,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;IACtD,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;IACnC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAC3D,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAE,MAAc,EAAE,KAAY;IAC1D,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IACvD,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;IAChD,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,0BAA0B;IAEpD,8BAA8B;IAC9B,kDAAkD;IAClD,uCAAuC;IACvC,6DAA6D;IAE7D,sCAAsC;IACtC,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;QACvB,mEAAmE;QACnE,KAAK,IAAI,SAAS,CAAC,CAAC,qEAAqE;QACzF,KAAK,IAAI,GAAG,CAAC,CAAC,eAAe;IAC/B,CAAC;IACD,MAAM,WAAW,GAAG,MAAM,GAAG,UAAU,CAAC;IACxC,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;IAC5E,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,+BAA+B;IAC3D,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,oCAAoC;IAC7D,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,kCAAkC;IACnE,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,wBAAwB;IACrD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;AAC3D,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAa,EAAE,CAAM;IAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;AACL,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAc,EAAE,KAAU;IACpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC1E,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC;AAED,mFAAmF;AACnF,iDAAiD;AACjD,4CAA4C;AAC5C,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAc,CAAC;AACnD,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAe,CAAC;AAEpD,SAAS,IAAI,CAAC,CAAM;IAClB,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,OAAO,CAAC,CAAS;IACxB,IAAI,CAAC,KAAK,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;AACjD,CAAC;AAeD;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,IAAI,CAAqB,CAAsB,EAAE,IAAY;IAC3E,OAAO;QACL,eAAe,EAAE,QAAQ;QAEzB,cAAc,CAAC,GAAM;YACnB,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,uCAAuC;QACvC,YAAY,CAAC,GAAM,EAAE,CAAS,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI;YACxC,IAAI,CAAC,GAAM,GAAG,CAAC;YACf,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;gBACf,IAAI,CAAC,GAAG,GAAG;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;gBACf,CAAC,KAAK,GAAG,CAAC;YACZ,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC;QAED;;;;;;;;;;;WAWG;QACH,gBAAgB,CAAC,GAAM,EAAE,CAAS;YAChC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,MAAM,GAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,GAAM,GAAG,CAAC;YACf,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;gBAChD,IAAI,GAAG,CAAC,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,oBAAoB;gBACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;oBACpC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpB,CAAC;gBACD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED;;;;;;WAMG;QACH,IAAI,CAAC,CAAS,EAAE,WAAgB,EAAE,CAAS;YACzC,mBAAmB;YACnB,gHAAgH;YAChH,mDAAmD;YACnD,qEAAqE;YACrE,gDAAgD;YAChD,eAAe;YACf,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACf,6FAA6F;YAC7F,qFAAqF;YACrF,0EAA0E;YAC1E,+EAA+E;YAC/E,2EAA2E;YAC3E,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9B,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;gBACnD,qFAAqF;gBACrF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACrF,CAAC,GAAG,KAAK,CAAC;gBACV,IAAI,MAAM,EAAE,CAAC;oBACX,wCAAwC;oBACxC,6EAA6E;oBAC7E,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,kCAAkC;oBAClC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YACD,OAAO,CAAC,CAAC,CAAC,CAAC;YACX,2DAA2D;YAC3D,wEAAwE;YACxE,4DAA4D;YAC5D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAClB,CAAC;QAED;;;;;;;WAOG;QACH,UAAU,CAAC,CAAS,EAAE,WAAgB,EAAE,CAAS,EAAE,MAAS,CAAC,CAAC,IAAI;YAChE,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9B,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;gBACnD,IAAI,CAAC,KAAK,GAAG;oBAAE,MAAM,CAAC,2BAA2B;gBACjD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACpE,CAAC,GAAG,KAAK,CAAC;gBACV,IAAI,MAAM,EAAE,CAAC;oBACX,sCAAsC;oBACtC,uBAAuB;oBACvB,SAAS;gBACX,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;oBACjC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0CAA0C;gBACzF,CAAC;YACH,CAAC;YACD,OAAO,CAAC,CAAC,CAAC,CAAC;YACX,OAAO,GAAG,CAAC;QACb,CAAC;QAED,cAAc,CAAC,CAAS,EAAE,CAAI,EAAE,SAAqB;YACnD,yDAAyD;YACzD,IAAI,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAQ,CAAC;gBAC1C,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACZ,oDAAoD;oBACpD,IAAI,OAAO,SAAS,KAAK,UAAU;wBAAE,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC5D,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,UAAU,CAAC,CAAI,EAAE,CAAS,EAAE,SAAqB;YAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,gBAAgB,CAAC,CAAI,EAAE,CAAS,EAAE,SAAqB,EAAE,IAAQ;YAC/D,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC;gBAAE,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,+BAA+B;YAClF,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3E,CAAC;QAED,mEAAmE;QACnE,wDAAwD;QACxD,2EAA2E;QAE3E,aAAa,CAAC,CAAI,EAAE,CAAS;YAC3B,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YACnB,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAC3B,CAAsB,EACtB,KAAQ,EACR,EAAU,EACV,EAAU;IAEV,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;IAChB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;IAChB,OAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;QAC5B,IAAI,EAAE,GAAG,GAAG;YAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,EAAE,GAAG,GAAG;YAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACnB,EAAE,KAAK,GAAG,CAAC;QACX,EAAE,KAAK,GAAG,CAAC;IACb,CAAC;IACD,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,SAAS,CACvB,CAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,OAAiB;IAEjB,+EAA+E;IAC/E,wEAAwE;IACxE,QAAQ;IACR,yCAAyC;IACzC,8DAA8D;IAC9D,2BAA2B;IAC3B,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7B,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/B,IAAI,OAAO,KAAK,OAAO;QAAE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IAChG,sEAAsE;IACtE,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACpB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACtC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,OAAO;IAC3B,IAAI,KAAK,GAAG,EAAE;QAAE,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;SAClC,IAAI,KAAK,GAAG,CAAC;QAAE,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;SACtC,IAAI,KAAK,GAAG,CAAC;QAAE,UAAU,GAAG,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IACjC,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;IAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC;IACzE,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACnD,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,0DAA0D;QAC3E,wCAAwC;QACxC,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QACD,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC;YAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE;gBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;IACvE,CAAC;IACD,OAAO,GAAQ,CAAC;AAClB,CAAC;AACD;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CACjC,CAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB;IAElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACpB,MAAM,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,4BAA4B;IACnE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,iBAAiB;IACrE,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAI,EAAE,EAAE;QACjC,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,OAAiB,EAAK,EAAE;QAC9B,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,IAAI,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;YAChC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,kDAAkD;YAClD,IAAI,GAAG,KAAK,IAAI;gBAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE;oBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC1E,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;YACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI;oBAAE,SAAS,CAAC,2BAA2B;gBAChD,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;AACJ,CAAC;AAkBD,eAAe;AACf,kBAAkB;AAClB,MAAM,UAAU,aAAa,CAC3B,KAAyB;IAUzB,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACxB,cAAc,CACZ,KAAK,EACL;QACE,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,OAAO;KACZ,EACD;QACE,UAAU,EAAE,eAAe;QAC3B,WAAW,EAAE,eAAe;KAC7B,CACF,CAAC;IACF,eAAe;IACf,OAAO,MAAM,CAAC,MAAM,CAAC;QACnB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;QACrC,GAAG,KAAK;QACR,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;KAChB,CAAC,CAAC;AACd,CAAC;AAWD,SAAS,WAAW,CAAI,KAAa,EAAE,KAAiB;IACtD,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC7F,aAAa,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,KAAK,CAAyB,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,8CAA8C;AAC9C,MAAM,UAAU,kBAAkB,CAChC,IAA+B,EAC/B,KAA0B,EAC1B,YAA8B,EAAE;IAEhC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,eAAe,CAAC,CAAC;IAChG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAU,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;IAC1D,CAAC;IACD,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;IAC9C,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;IAC9C,MAAM,EAAE,GAAc,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACzD,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAU,CAAC;IAC9C,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;QACvB,aAAa;QACb,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;IAC1E,CAAC;IACD,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpB,CAAC"}