{"version": 3, "file": "weierstrass.d.ts", "sourceRoot": "", "sources": ["../../src/abstract/weierstrass.ts"], "names": [], "mappings": "AA4BA,OAAO,EAiBL,KAAK,KAAK,EACV,KAAK,GAAG,EACR,KAAK,OAAO,EACb,MAAM,aAAa,CAAC;AACrB,OAAO,EAOL,KAAK,WAAW,EAChB,KAAK,UAAU,EACf,KAAK,KAAK,EACV,KAAK,gBAAgB,EACtB,MAAM,YAAY,CAAC;AACpB,OAAO,EAML,KAAK,MAAM,EACX,KAAK,OAAO,EACb,MAAM,cAAc,CAAC;AAEtB,YAAY,EAAE,WAAW,EAAE,CAAC;AAC5B,MAAM,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,QAAQ,EAAE,UAAU,EAAE,KAAK,UAAU,CAAC;AACpF;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,OAAO,CAAC;QAAC,EAAE,EAAE,MAAM,CAAA;KAAE,CAAC;CACxF,CAAC;AACF,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG;IAE3C,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;IAGL,wBAAwB,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC;IAC7C,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,IAAI,CAAC,EAAE,gBAAgB,CAAC;IAGxB,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC;IAE5E,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC;CACtF,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;AACpC,MAAM,MAAM,QAAQ,GAAG;IAAE,IAAI,CAAC,EAAE,OAAO,CAAC;IAAC,YAAY,CAAC,EAAE,OAAO,CAAC;IAAC,OAAO,CAAC,EAAE,OAAO,CAAA;CAAE,CAAC;AACrF,MAAM,MAAM,OAAO,GAAG;IACpB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,MAAM,CAAC,EAAE,SAAS,GAAG,KAAK,GAAG,IAAI,GAAG,SAAS,CAAC;CAC/C,CAAC;AAOF,0CAA0C;AAC1C,MAAM,WAAW,aAAa,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC/D,uDAAuD;IACvD,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IACf,uDAAuD;IACvD,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IACf,8BAA8B;IAC9B,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IACf,0BAA0B;IAC1B,IAAI,CAAC,IAAI,CAAC,CAAC;IACX,0BAA0B;IAC1B,IAAI,CAAC,IAAI,CAAC,CAAC;IACX,cAAc,IAAI,IAAI,CAAC;IACvB,aAAa,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;IAClC,GAAG,IAAI,OAAO,CAAC;IACf,aAAa,IAAI,OAAO,CAAC;IACzB,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACjD;;;;;;;;OAQG;IACH,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAEpE,mEAAmE;IACnE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACxC,kGAAkG;IAClG,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,GAAG,UAAU,CAAC;IAC5C,KAAK,CAAC,YAAY,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC;IAEtC,gCAAgC;IAChC,UAAU,CAAC,YAAY,CAAC,EAAE,OAAO,GAAG,UAAU,CAAC;IAC/C,uCAAuC;IACvC,oBAAoB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IAC9F,wCAAwC;IACxC,QAAQ,IAAI,OAAO,CAAC;IACpB,iDAAiD;IACjD,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1C;AAED,wCAAwC;AACxC,MAAM,WAAW,eAAe,CAAC,CAAC,CAAE,SAAQ,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5E,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,wEAAwE;IACxE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACzC,wEAAwE;IACxE,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAChD,SAAS,CAAC,YAAY,EAAE,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACpC,cAAc,CAAC,UAAU,EAAE,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACtD,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3D,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CACtE;AAED,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG;IAChD,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;IAClD,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,OAAO,KAAK,UAAU,CAAC;CACjG,CAAC;AAGF,MAAM,MAAM,yBAAyB,CAAC,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAG3F,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI;IAC9B,iDAAiD;IACjD,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1B,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1B,8BAA8B;IAC9B,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACpC,kBAAkB;IAClB,sBAAsB,EAAE,CAAC,GAAG,EAAE,OAAO,KAAK,MAAM,CAAC;IACjD,kBAAkB;IAClB,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACjC,kDAAkD;IAClD,kBAAkB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC;CAC9C,CAAC;AAUF;;;;;;;;;;GAUG;AACH,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI,QAAQ,CAAC;IACxC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;IACL,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;CACP,CAAC,CAAC;AAMH,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,OAAO,CAAC;IAC5C,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAEnB,wBAAwB,EAAE,SAAS,MAAM,EAAE,CAAC;IAC5C,kBAAkB,EAAE,OAAO,CAAC;IAC5B,IAAI,EAAE,gBAAgB,CAAC;IACvB,cAAc,EAAE,OAAO,CAAC;IACxB,aAAa,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC;IAC3E,aAAa,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC;IACpF,SAAS,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,OAAO,KAAK,UAAU,CAAC;CAChG,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG;IACtB,IAAI,EAAE,KAAK,CAAC;IACZ,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IACnD,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,MAAM,CAAC;IACzC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,MAAM,CAAC;CAC/C,CAAC;AAEF,4EAA4E;AAC5E,MAAM,WAAW,KAAK;IACpB,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,OAAO,KAAK,UAAU,CAAC;IAC1E,eAAe,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,OAAO,KAAK,UAAU,CAAC;IACzF,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK,sBAAsB,CAAC;IAClF,MAAM,EAAE,CAAC,SAAS,EAAE,GAAG,GAAG,aAAa,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC;IAClG,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,SAAS,EAAE,oBAAoB,CAAC;IAChC,KAAK,EAAE;QACL,iBAAiB,CAAC,UAAU,EAAE,OAAO,GAAG,OAAO,CAAC;QAChD,gBAAgB,EAAE,MAAM,UAAU,CAAC;QAEnC,sBAAsB,EAAE,CAAC,GAAG,EAAE,OAAO,KAAK,MAAM,CAAC;QACjD,kBAAkB;QAClB,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC,MAAM,CAAC,CAAC;KAC3F,CAAC;CACH;AACD,qBAAa,MAAO,SAAQ,KAAK;gBACnB,CAAC,SAAK;CAGnB;AACD,MAAM,MAAM,IAAI,GAAG;IAEjB,GAAG,EAAE,OAAO,MAAM,CAAC;IAEnB,IAAI,EAAE;QACJ,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,CAAC;QAE9C,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,GAAG;YAAE,CAAC,EAAE,UAAU,CAAC;YAAC,CAAC,EAAE,UAAU,CAAA;SAAE,CAAC;KACzE,CAAC;IAKF,IAAI,EAAE;QACJ,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;QAC5B,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,MAAM,CAAC;KAClC,CAAC;IACF,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC1D,UAAU,CAAC,GAAG,EAAE;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,MAAM,CAAC;CACnD,CAAC;AACF;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE,IAoFjB,CAAC;AAOF,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAW5E;AACD,wBAAgB,qBAAqB,CACnC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAClB,wBAAwB,CAAC,EAAE,SAAS,MAAM,EAAE,EAC5C,cAAc,CAAC,EAAE,OAAO,GACvB,CAAC,GAAG,EAAE,OAAO,KAAK,MAAM,CA6B1B;AAED,wBAAgB,YAAY,CAAC,CAAC,EAC5B,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,EACzB,SAAS,GAAE,oBAAoB,CAAC,CAAC,CAAM,GACtC,eAAe,CAAC,CAAC,CAAC,CAmfpB;AAGD,qCAAqC;AACrC,wBAAgB,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAIvF;AAGD,MAAM,WAAW,aAAa;IAC5B,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC3B,cAAc,IAAI,IAAI,CAAC;IACvB,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,sBAAsB,CAAC;IACzD,QAAQ,IAAI,OAAO,CAAC;IACpB,UAAU,IAAI,aAAa,CAAC;IAC5B,gBAAgB,CAAC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACtD,iBAAiB,IAAI,UAAU,CAAC;IAChC,YAAY,IAAI,MAAM,CAAC;IACvB,aAAa,IAAI,UAAU,CAAC;IAC5B,QAAQ,IAAI,MAAM,CAAC;CAEpB;AACD,MAAM,MAAM,sBAAsB,GAAG,aAAa,GAAG;IACnD,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;IAC7D,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC;IACrC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC;CAClC,CAAC;AACF,MAAM,MAAM,aAAa,GAAG;IAAE,CAAC,EAAE,MAAM,CAAC;IAAC,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AACrD,MAAM,MAAM,MAAM,GAAG,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AAEjD,MAAM,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG;IAC5C,IAAI,EAAE,KAAK,CAAC;IACZ,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IACnD,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,MAAM,CAAC;IACzC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,MAAM,CAAC;CAC/C,CAAC;AAOF,MAAM,MAAM,OAAO,GAAG;IACpB,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,OAAO,KAAK,UAAU,CAAC;IAC1E,eAAe,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,OAAO,KAAK,UAAU,CAAC;IACzF,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK,sBAAsB,CAAC;IAClF,MAAM,EAAE,CAAC,SAAS,EAAE,GAAG,GAAG,aAAa,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC;IAClG,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,8BAA8B;IAC9B,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IACzC,SAAS,EAAE,oBAAoB,CAAC;IAChC,KAAK,EAAE;QACL,sBAAsB,EAAE,CAAC,GAAG,EAAE,OAAO,KAAK,MAAM,CAAC;QACjD,iBAAiB,CAAC,UAAU,EAAE,OAAO,GAAG,OAAO,CAAC;QAChD,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC,MAAM,CAAC,CAAC;KAC3F,CAAC;CACH,CAAC;AAEF,wBAAgB,KAAK,CACnB,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,EAC9B,SAAS,EAAE,SAAS,EACpB,SAAS,GAAE,oBAAoB,CAAC,MAAM,CAAM,GAC3C,KAAK,CA4aP;AAED,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI;IAC/B,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1B,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC;CACpC,CAAC;AACF,MAAM,MAAM,UAAU,GAAG;IACvB,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,SAAS,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACxC,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AA0EF,wBAAgB,WAAW,CAAC,CAAC,EAAE,SAAS,GAAG,OAAO,CAKjD;AAED;;;;;;;;GAQG;AACH,wBAAgB,cAAc,CAAC,CAAC,EAC9B,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EACb,CAAC,EAAE,CAAC,GACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;IAAE,OAAO,EAAE,OAAO,CAAC;IAAC,KAAK,EAAE,CAAC,CAAA;CAAE,CAmEhD;AACD;;;GAGG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,EACnC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EACb,IAAI,EAAE;IACJ,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;CACN,GACA,CAAC,CAAC,EAAE,CAAC,KAAK;IAAE,CAAC,EAAE,CAAC,CAAC;IAAC,CAAC,EAAE,CAAC,CAAA;CAAE,CAwC1B"}