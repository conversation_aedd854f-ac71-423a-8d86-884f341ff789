/// <reference types="jest" />
import { CoinbaseWalletProvider, CoinbaseWalletProviderOptions } from '../provider/CoinbaseWalletProvider';
export declare const mockSetAppInfo: jest.Mock<any, any>;
export declare class MockProviderClass extends CoinbaseWalletProvider {
    constructor(opts: Readonly<CoinbaseWalletProviderOptions>);
    close(): Promise<void>;
    private initializeRelay;
}
export declare const mockExtensionProvider: MockProviderClass;
