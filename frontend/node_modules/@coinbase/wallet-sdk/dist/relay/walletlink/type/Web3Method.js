"use strict";
// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>
// Licensed under the Apache License, version 2.0
Object.defineProperty(exports, "__esModule", { value: true });
exports.web3Methods = void 0;
exports.web3Methods = [
    'requestEthereumAccounts',
    'signEthereumMessage',
    'signEthereumTransaction',
    'submitEthereumTransaction',
    'ethereumAddressFromSignedMessage',
    'scanQRCode',
    'generic',
    'childRequestEthereumAccounts',
    'addEthereumChain',
    'switchEthereumChain',
    'makeEthereumJSONRPCRequest',
    'watchAsset',
    'selectProvider',
    'connectAndSignIn',
];
