import { EventEmitter } from 'eventemitter3';
import { AddressString, Callback, HexString, ProviderType } from '../core/type';
import { ScopedLocalStorage } from '../lib/ScopedLocalStorage';
import { RelayAbstract } from '../relay/RelayAbstract';
import { RelayEventManager } from '../relay/RelayEventManager';
import { DiagnosticLogger } from './DiagnosticLogger';
import { JSONRPCRequest, JSONRPCResponse } from './JSONRPC';
import { RequestArguments, Web3Provider } from './Web3Provider';
export interface CoinbaseWalletProviderOptions {
    chainId: number;
    jsonRpcUrl: string;
    qrUrl?: string | null;
    overrideIsCoinbaseWallet?: boolean;
    overrideIsCoinbaseBrowser?: boolean;
    overrideIsMetaMask: boolean;
    relayEventManager: RelayEventManager;
    relayProvider: () => Promise<RelayAbstract>;
    storage: ScopedLocalStorage;
    diagnosticLogger?: DiagnosticLogger;
}
export declare class CoinbaseWalletProvider extends EventEmitter implements Web3Provider {
    readonly isCoinbaseWallet: boolean;
    readonly isCoinbaseBrowser: boolean;
    readonly qrUrl?: string | null;
    reloadOnDisconnect: boolean;
    private readonly _filterPolyfill;
    private readonly _subscriptionManager;
    private readonly _relayProvider;
    private _relay;
    private readonly _storage;
    private readonly _relayEventManager;
    private readonly diagnostic?;
    private _chainIdFromOpts;
    private _jsonRpcUrlFromOpts;
    private readonly _overrideIsMetaMask;
    private _addresses;
    private hasMadeFirstChainChangedEmission;
    constructor(options: Readonly<CoinbaseWalletProviderOptions>);
    /** @deprecated Use `.request({ method: 'eth_accounts' })` instead. */
    get selectedAddress(): AddressString | undefined;
    /** @deprecated Use the chain ID. If you still need the network ID, use `.request({ method: 'net_version' })`. */
    get networkVersion(): string;
    /** @deprecated Use `.request({ method: 'eth_chainId' })` instead. */
    get chainId(): string;
    get isWalletLink(): boolean;
    /**
     * Some DApps (i.e. Alpha Homora) seem to require the window.ethereum object return
     * true for this method.
     */
    get isMetaMask(): boolean;
    get host(): string;
    get connected(): boolean;
    isConnected(): boolean;
    private get jsonRpcUrl();
    private set jsonRpcUrl(value);
    disableReloadOnDisconnect(): void;
    setProviderInfo(jsonRpcUrl: string, chainId: number): void;
    private updateProviderInfo;
    private watchAsset;
    private addEthereumChain;
    private switchEthereumChain;
    setAppInfo(appName: string, appLogoUrl: string | null): void;
    /** @deprecated Use `.request({ method: 'eth_requestAccounts' })` instead. */
    enable(): Promise<AddressString[]>;
    close(): Promise<void>;
    /** @deprecated Use `.request(...)` instead. */
    send(request: JSONRPCRequest): JSONRPCResponse;
    send(request: JSONRPCRequest[]): JSONRPCResponse[];
    send(request: JSONRPCRequest, callback: Callback<JSONRPCResponse>): void;
    send(request: JSONRPCRequest[], callback: Callback<JSONRPCResponse[]>): void;
    send<T = any>(method: string, params?: any[] | any): Promise<T>;
    private _send;
    /** @deprecated Use `.request(...)` instead. */
    sendAsync(request: JSONRPCRequest, callback: Callback<JSONRPCResponse>): void;
    sendAsync(request: JSONRPCRequest[], callback: Callback<JSONRPCResponse[]>): void;
    private _sendAsync;
    request<T>(args: RequestArguments): Promise<T>;
    private _request;
    scanQRCode(match?: RegExp): Promise<string>;
    genericRequest(data: object, action: string): Promise<string>;
    /**
     * @beta
     * This method is currently in beta. While it is available for use, please note that it is still under testing and may undergo significant changes.
     *
     * @remarks
     * IMPORTANT: Signature validation is not performed by this method. Users of this method are advised to perform their own signature validation.
     * Common web3 frontend libraries such as ethers.js and viem provide the `verifyMessage` utility function that can be used for signature validation.
     *
     * It combines `eth_requestAccounts` and "Sign-In with Ethereum" (EIP-4361) into a single call.
     * The returned account and signed message can be used to authenticate the user.
     *
     * @param {Object} params - An object with the following properties:
     * - `nonce` {string}: A unique string to prevent replay attacks.
     * - `statement` {string}: An optional human-readable ASCII assertion that the user will sign.
     * - `resources` {string[]}: An optional list of information the user wishes to have resolved as part of authentication by the relying party.
     *
     * @returns {Promise<ConnectAndSignInResponse>} A promise that resolves to an object with the following properties:
     * - `accounts` {string[]}: The Ethereum accounts of the user.
     * - `message` {string}: The overall message that the user signed. Hex encoded.
     * - `signature` {string}: The signature of the message, signed with the user's private key. Hex encoded.
     */
    connectAndSignIn(params: {
        nonce: string;
        statement?: string;
        resources?: string[];
    }): Promise<{
        accounts: AddressString[];
        message: HexString;
        signature: HexString;
    }>;
    selectProvider(providerOptions: ProviderType[]): Promise<ProviderType>;
    supportsSubscriptions(): boolean;
    subscribe(): void;
    unsubscribe(): void;
    disconnect(): boolean;
    private _sendRequest;
    protected _setAddresses(addresses: string[], _?: boolean): void;
    private _sendRequestAsync;
    private _sendMultipleRequestsAsync;
    private _handleSynchronousMethods;
    private _handleAsynchronousMethods;
    private _handleAsynchronousFilterMethods;
    private _handleSubscriptionMethods;
    private _isKnownAddress;
    private _ensureKnownAddress;
    private _prepareTransactionParams;
    protected _isAuthorized(): boolean;
    private _requireAuthorization;
    private _throwUnsupportedMethodError;
    private _signEthereumMessage;
    private _ethereumAddressFromSignedMessage;
    private _eth_accounts;
    private _eth_coinbase;
    private _net_version;
    private _eth_chainId;
    private getChainId;
    private _eth_requestAccounts;
    private _eth_sign;
    private _eth_ecRecover;
    private _personal_sign;
    private _personal_ecRecover;
    private _eth_signTransaction;
    private _eth_sendRawTransaction;
    private _eth_sendTransaction;
    private _eth_signTypedData_v1;
    private _eth_signTypedData_v3;
    private _eth_signTypedData_v4;
    /** @deprecated */
    private _cbwallet_arbitrary;
    private _wallet_addEthereumChain;
    private _wallet_switchEthereumChain;
    private _wallet_watchAsset;
    private _eth_uninstallFilter;
    private _eth_newFilter;
    private _eth_newBlockFilter;
    private _eth_newPendingTransactionFilter;
    private _eth_getFilterChanges;
    private _eth_getFilterLogs;
    private initializeRelay;
}
