"use strict";
// DiagnosticLogger for debugging purposes only
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVENTS = void 0;
exports.EVENTS = {
    STARTED_CONNECTING: 'walletlink_sdk.started.connecting',
    CONNECTED_STATE_CHANGE: 'walletlink_sdk.connected',
    DISCONNECTED: 'walletlink_sdk.disconnected',
    METADATA_DESTROYED: 'walletlink_sdk_metadata_destroyed',
    LINKED: 'walletlink_sdk.linked',
    FAILURE: 'walletlink_sdk.generic_failure',
    SESSION_CONFIG_RECEIVED: 'walletlink_sdk.session_config_event_received',
    ETH_ACCOUNTS_STATE: 'walletlink_sdk.eth_accounts_state',
    SESSION_STATE_CHANGE: 'walletlink_sdk.session_state_change',
    UNLINKED_ERROR_STATE: 'walletlink_sdk.unlinked_error_state',
    SKIPPED_CLEARING_SESSION: 'walletlink_sdk.skipped_clearing_session',
    GENERAL_ERROR: 'walletlink_sdk.general_error',
    WEB3_REQUEST: 'walletlink_sdk.web3.request',
    WEB3_REQUEST_PUBLISHED: 'walletlink_sdk.web3.request_published',
    WEB3_RESPONSE: 'walletlink_sdk.web3.response',
    METHOD_NOT_IMPLEMENTED: 'walletlink_sdk.method_not_implemented',
    UNKNOWN_ADDRESS_ENCOUNTERED: 'walletlink_sdk.unknown_address_encountered',
};
