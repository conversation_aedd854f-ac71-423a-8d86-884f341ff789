{"version": 3, "file": "misc.mjs", "sourceRoot": "", "sources": ["../src/misc.ts"], "names": [], "mappings": "AAAA,EAAE;AACF,QAAQ;AACR,EAAE;AAoCF,EAAE;AACF,cAAc;AACd,EAAE;AAEF;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAC7B,KAAgB;IAEhB,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9E,CAAC;AAED,EAAE;AACF,0BAA0B;AAC1B,EAAE;AAEF;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAKzB,aAA4B,EAC5B,IAAc,EAKZ,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAEvD;;;;;;;;;;GAUG;AACH,MAAM,UAAU,qBAAqB,CACnC,MAAiC;IAEjC,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAU,CAAC;AACrD,CAAC;AAID;;GAEG;AACH,MAAM,CAAN,IAAY,QAUX;AAVD,WAAY,QAAQ;IAClB,uCAAQ,CAAA;IACR,yCAAS,CAAA;IACT,6CAAW,CAAA;IACX,uCAAQ,CAAA;IACR,yCAAS,CAAA;IACT,yCAAS,CAAA;IACT,yCAAS,CAAA;IACT,wDAAwD;IACxD,wCAAS,CAAA;AACX,CAAC,EAVW,QAAQ,GAAR,QAAQ,KAAR,QAAQ,QAUnB;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,iBAAiB,CAAC;AAE1D;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,KAAc;IAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IAED,IAAI;QACF,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,OAAO,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;YAC5C,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SACtC;QAED,OAAO,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;KAC/C;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,OAAO,CAAC,SAAiB;IACvC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAa;IAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;QACvD,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE;YACtB,OAAO,KAAK,GAAG,CAAC,CAAC;SAClB;QACD,OAAO,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,oDAAoD;IACpD,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;AACrE,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAa;IAC/C,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;AACjC,CAAC", "sourcesContent": ["//\n// Types\n//\n\n/**\n * Makes every specified property of the specified object type mutable.\n *\n * @template ObjectValue - The object whose readonly properties to make mutable.\n * @template TargetKey - The property key(s) to make mutable.\n */\nexport type Mutable<\n  ObjectValue extends Record<string, unknown>,\n  TargetKey extends keyof ObjectValue,\n> = {\n  -readonly [Key in keyof Pick<ObjectValue, TargetKey>]: ObjectValue[Key];\n} & {\n  [Key in keyof Omit<ObjectValue, TargetKey>]: ObjectValue[Key];\n};\n\n/**\n * Useful for representing some value that _might_ be present and / or complete.\n *\n * @template Value - The value that might be present or complete.\n */\nexport type PartialOrAbsent<Value> = Partial<Value> | null | undefined;\n\n/**\n * Like {@link Array}, but always non-empty.\n *\n * @template Element - The non-empty array member type.\n */\nexport type NonEmptyArray<Element> = [Element, ...Element[]];\n\n/**\n * A JavaScript object that is not `null`, a function, or an array.\n */\nexport type RuntimeObject = Record<PropertyKey, unknown>;\n\n//\n// Type Guards\n//\n\n/**\n * A {@link NonEmptyArray} type guard.\n *\n * @template Element - The non-empty array member type.\n * @param value - The value to check.\n * @returns Whether the value is a non-empty array.\n */\nexport function isNonEmptyArray<Element>(\n  value: Element[],\n): value is NonEmptyArray<Element> {\n  return Array.isArray(value) && value.length > 0;\n}\n\n/**\n * Type guard for \"nullishness\".\n *\n * @param value - Any value.\n * @returns `true` if the value is null or undefined, `false` otherwise.\n */\nexport function isNullOrUndefined(value: unknown): value is null | undefined {\n  return value === null || value === undefined;\n}\n\n/**\n * A type guard for {@link RuntimeObject}.\n *\n * @param value - The value to check.\n * @returns Whether the specified value has a runtime type of `object` and is\n * neither `null` nor an `Array`.\n */\nexport function isObject(value: unknown): value is RuntimeObject {\n  return Boolean(value) && typeof value === 'object' && !Array.isArray(value);\n}\n\n//\n// Other utility functions\n//\n\n/**\n * A type guard for ensuring an object has a property.\n *\n * @param objectToCheck - The object to check.\n * @param name - The property name to check for.\n * @returns Whether the specified object has an own property with the specified\n * name, regardless of whether it is enumerable or not.\n */\nexport const hasProperty = <\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  ObjectToCheck extends Object,\n  Property extends PropertyKey,\n>(\n  objectToCheck: ObjectToCheck,\n  name: Property,\n): objectToCheck is ObjectToCheck &\n  Record<\n    Property,\n    Property extends keyof ObjectToCheck ? ObjectToCheck[Property] : unknown\n  > => Object.hasOwnProperty.call(objectToCheck, name);\n\n/**\n * `Object.getOwnPropertyNames()` is intentionally generic: it returns the\n * immediate property names of an object, but it cannot make guarantees about\n * the contents of that object, so the type of the property names is merely\n * `string[]`. While this is technically accurate, it is also unnecessary if we\n * have an object with a type that we own (such as an enum).\n *\n * @param object - The plain object.\n * @returns The own property names of the object which are assigned a type\n * derived from the object itself.\n */\nexport function getKnownPropertyNames<Key extends PropertyKey>(\n  object: Partial<Record<Key, any>>,\n): Key[] {\n  return Object.getOwnPropertyNames(object) as Key[];\n}\n\nexport type PlainObject = Record<number | string | symbol, unknown>;\n\n/**\n * Predefined sizes (in Bytes) of specific parts of JSON structure.\n */\nexport enum JsonSize {\n  Null = 4,\n  Comma = 1,\n  Wrapper = 1,\n  True = 4,\n  False = 5,\n  Quote = 1,\n  Colon = 1,\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  Date = 24,\n}\n\n/**\n * Regular expression with pattern matching for (special) escaped characters.\n */\nexport const ESCAPE_CHARACTERS_REGEXP = /\"|\\\\|\\n|\\r|\\t/gu;\n\n/**\n * Check if the value is plain object.\n *\n * @param value - Value to be checked.\n * @returns True if an object is the plain JavaScript object,\n * false if the object is not plain (e.g. function).\n */\nexport function isPlainObject(value: unknown): value is PlainObject {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n\n  try {\n    let proto = value;\n    while (Object.getPrototypeOf(proto) !== null) {\n      proto = Object.getPrototypeOf(proto);\n    }\n\n    return Object.getPrototypeOf(value) === proto;\n  } catch (_) {\n    return false;\n  }\n}\n\n/**\n * Check if character is ASCII.\n *\n * @param character - Character.\n * @returns True if a character code is ASCII, false if not.\n */\nexport function isASCII(character: string) {\n  return character.charCodeAt(0) <= 127;\n}\n\n/**\n * Calculate string size.\n *\n * @param value - String value to calculate size.\n * @returns Number of bytes used to store whole string value.\n */\nexport function calculateStringSize(value: string): number {\n  const size = value.split('').reduce((total, character) => {\n    if (isASCII(character)) {\n      return total + 1;\n    }\n    return total + 2;\n  }, 0);\n\n  // Also detect characters that need backslash escape\n  return size + (value.match(ESCAPE_CHARACTERS_REGEXP) ?? []).length;\n}\n\n/**\n * Calculate size of a number ofter JSON serialization.\n *\n * @param value - Number value to calculate size.\n * @returns Number of bytes used to store whole number in JSON.\n */\nexport function calculateNumberSize(value: number): number {\n  return value.toString().length;\n}\n"]}