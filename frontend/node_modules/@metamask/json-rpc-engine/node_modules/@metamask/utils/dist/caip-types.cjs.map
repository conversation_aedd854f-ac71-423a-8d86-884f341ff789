{"version": 3, "file": "caip-types.cjs", "sourceRoot": "", "sources": ["../src/caip-types.ts"], "names": [], "mappings": ";;;AACA,uDAA4D;AAE/C,QAAA,mBAAmB,GAC9B,mEAAmE,CAAC;AAEzD,QAAA,oBAAoB,GAAG,mBAAmB,CAAC;AAE3C,QAAA,oBAAoB,GAAG,wBAAwB,CAAC;AAEhD,QAAA,qBAAqB,GAChC,wHAAwH,CAAC;AAE9G,QAAA,0BAA0B,GAAG,0BAA0B,CAAC;AAErE;;GAEG;AACU,QAAA,iBAAiB,GAAG,IAAA,qBAAO,EAAC,IAAA,oBAAM,GAAE,EAAE,2BAAmB,CAAC,CAAC;AAGxE;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,qBAAO,EAAC,IAAA,oBAAM,GAAE,EAAE,4BAAoB,CAAC,CAAC;AAG3E;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,qBAAO,EAAC,IAAA,oBAAM,GAAE,EAAE,4BAAoB,CAAC,CAAC;AAG3E;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,qBAAO,EAAC,IAAA,oBAAM,GAAE,EAAE,6BAAqB,CAAC,CAAC;AAG5E;;GAEG;AACU,QAAA,wBAAwB,GAAG,IAAA,qBAAO,EAC7C,IAAA,oBAAM,GAAE,EACR,kCAA0B,CAC3B,CAAC;AAGF,6BAA6B;AAC7B,IAAY,kBAGX;AAHD,WAAY,kBAAkB;IAC5B,iCAAiC;IACjC,uCAAiB,CAAA;AACnB,CAAC,EAHW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAG7B;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAc;IAC1C,OAAO,IAAA,gBAAE,EAAC,KAAK,EAAE,yBAAiB,CAAC,CAAC;AACtC,CAAC;AAFD,sCAEC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,IAAA,gBAAE,EAAC,KAAK,EAAE,2BAAmB,CAAC,CAAC;AACxC,CAAC;AAFD,0CAEC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,IAAA,gBAAE,EAAC,KAAK,EAAE,2BAAmB,CAAC,CAAC;AACxC,CAAC;AAFD,0CAEC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,IAAA,gBAAE,EAAC,KAAK,EAAE,2BAAmB,CAAC,CAAC;AACxC,CAAC;AAFD,0CAEC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,KAAc;IAEd,OAAO,IAAA,gBAAE,EAAC,KAAK,EAAE,gCAAwB,CAAC,CAAC;AAC7C,CAAC;AAJD,oDAIC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,WAAwB;IAIvD,MAAM,KAAK,GAAG,2BAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;QAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;IAED,OAAO;QACL,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAA0B;QAClD,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAA0B;KACnD,CAAC;AACJ,CAAC;AAbD,4CAaC;AAED;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAAC,aAA4B;IAK7D,MAAM,KAAK,GAAG,6BAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;QAClB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAC7C;IAED,OAAO;QACL,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,cAAoC;QAC1D,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAsB;QAC5C,KAAK,EAAE;YACL,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAA0B;YAClD,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAA0B;SACnD;KACF,CAAC;AACJ,CAAC;AAlBD,gDAkBC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAgB,aAAa,CAC3B,SAAwB,EACxB,SAAwB;IAExB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CACb,oCAAoC,4BAAoB,CAAC,QAAQ,EAAE,EAAE,CACtE,CAAC;KACH;IAED,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CACb,oCAAoC,4BAAoB,CAAC,QAAQ,EAAE,EAAE,CACtE,CAAC;KACH;IAED,OAAO,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;AACrC,CAAC;AAjBD,sCAiBC", "sourcesContent": ["import type { Infer } from '@metamask/superstruct';\nimport { is, pattern, string } from '@metamask/superstruct';\n\nexport const CAIP_CHAIN_ID_REGEX =\n  /^(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32})$/u;\n\nexport const CAIP_NAMESPACE_REGEX = /^[-a-z0-9]{3,8}$/u;\n\nexport const CAIP_REFERENCE_REGEX = /^[-_a-zA-Z0-9]{1,32}$/u;\n\nexport const CAIP_ACCOUNT_ID_REGEX =\n  /^(?<chainId>(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32})):(?<accountAddress>[-.%a-zA-Z0-9]{1,128})$/u;\n\nexport const CAIP_ACCOUNT_ADDRESS_REGEX = /^[-.%a-zA-Z0-9]{1,128}$/u;\n\n/**\n * A CAIP-2 chain ID, i.e., a human-readable namespace and reference.\n */\nexport const CaipChainIdStruct = pattern(string(), CAIP_CHAIN_ID_REGEX);\nexport type CaipChainId = `${string}:${string}`;\n\n/**\n * A CAIP-2 namespace, i.e., the first part of a CAIP chain ID.\n */\nexport const CaipNamespaceStruct = pattern(string(), CAIP_NAMESPACE_REGEX);\nexport type CaipNamespace = Infer<typeof CaipNamespaceStruct>;\n\n/**\n * A CAIP-2 reference, i.e., the second part of a CAIP chain ID.\n */\nexport const CaipReferenceStruct = pattern(string(), CAIP_REFERENCE_REGEX);\nexport type CaipReference = Infer<typeof CaipReferenceStruct>;\n\n/**\n * A CAIP-10 account ID, i.e., a human-readable namespace, reference, and account address.\n */\nexport const CaipAccountIdStruct = pattern(string(), CAIP_ACCOUNT_ID_REGEX);\nexport type CaipAccountId = `${string}:${string}:${string}`;\n\n/**\n * A CAIP-10 account address, i.e., the third part of the CAIP account ID.\n */\nexport const CaipAccountAddressStruct = pattern(\n  string(),\n  CAIP_ACCOUNT_ADDRESS_REGEX,\n);\nexport type CaipAccountAddress = Infer<typeof CaipAccountAddressStruct>;\n\n/** Known CAIP namespaces. */\nexport enum KnownCaipNamespace {\n  /** EIP-155 compatible chains. */\n  Eip155 = 'eip155',\n}\n\n/**\n * Check if the given value is a {@link CaipChainId}.\n *\n * @param value - The value to check.\n * @returns Whether the value is a {@link CaipChainId}.\n */\nexport function isCaipChainId(value: unknown): value is CaipChainId {\n  return is(value, CaipChainIdStruct);\n}\n\n/**\n * Check if the given value is a {@link CaipNamespace}.\n *\n * @param value - The value to check.\n * @returns Whether the value is a {@link CaipNamespace}.\n */\nexport function isCaipNamespace(value: unknown): value is CaipNamespace {\n  return is(value, CaipNamespaceStruct);\n}\n\n/**\n * Check if the given value is a {@link CaipReference}.\n *\n * @param value - The value to check.\n * @returns Whether the value is a {@link CaipReference}.\n */\nexport function isCaipReference(value: unknown): value is CaipReference {\n  return is(value, CaipReferenceStruct);\n}\n\n/**\n * Check if the given value is a {@link CaipAccountId}.\n *\n * @param value - The value to check.\n * @returns Whether the value is a {@link CaipAccountId}.\n */\nexport function isCaipAccountId(value: unknown): value is CaipAccountId {\n  return is(value, CaipAccountIdStruct);\n}\n\n/**\n * Check if a value is a {@link CaipAccountAddress}.\n *\n * @param value - The value to validate.\n * @returns True if the value is a valid {@link CaipAccountAddress}.\n */\nexport function isCaipAccountAddress(\n  value: unknown,\n): value is CaipAccountAddress {\n  return is(value, CaipAccountAddressStruct);\n}\n\n/**\n * Parse a CAIP-2 chain ID to an object containing the namespace and reference.\n * This validates the CAIP-2 chain ID before parsing it.\n *\n * @param caipChainId - The CAIP-2 chain ID to validate and parse.\n * @returns The parsed CAIP-2 chain ID.\n */\nexport function parseCaipChainId(caipChainId: CaipChainId): {\n  namespace: CaipNamespace;\n  reference: CaipReference;\n} {\n  const match = CAIP_CHAIN_ID_REGEX.exec(caipChainId);\n  if (!match?.groups) {\n    throw new Error('Invalid CAIP chain ID.');\n  }\n\n  return {\n    namespace: match.groups.namespace as CaipNamespace,\n    reference: match.groups.reference as CaipReference,\n  };\n}\n\n/**\n * Parse an CAIP-10 account ID to an object containing the chain ID, parsed chain ID, and account address.\n * This validates the CAIP-10 account ID before parsing it.\n *\n * @param caipAccountId - The CAIP-10 account ID to validate and parse.\n * @returns The parsed CAIP-10 account ID.\n */\nexport function parseCaipAccountId(caipAccountId: CaipAccountId): {\n  address: CaipAccountAddress;\n  chainId: CaipChainId;\n  chain: { namespace: CaipNamespace; reference: CaipReference };\n} {\n  const match = CAIP_ACCOUNT_ID_REGEX.exec(caipAccountId);\n  if (!match?.groups) {\n    throw new Error('Invalid CAIP account ID.');\n  }\n\n  return {\n    address: match.groups.accountAddress as CaipAccountAddress,\n    chainId: match.groups.chainId as CaipChainId,\n    chain: {\n      namespace: match.groups.namespace as CaipNamespace,\n      reference: match.groups.reference as CaipReference,\n    },\n  };\n}\n\n/**\n * Chain ID as defined per the CAIP-2\n * {@link https://github.com/ChainAgnostic/CAIPs/blob/main/CAIPs/caip-2.md}.\n *\n * It defines a way to uniquely identify any blockchain in a human-readable\n * way.\n *\n * @param namespace - The standard (ecosystem) of similar blockchains.\n * @param reference - Identify of a blockchain within a given namespace.\n * @throws {@link Error}\n * This exception is thrown if the inputs does not comply with the CAIP-2\n * syntax specification\n * {@link https://github.com/ChainAgnostic/CAIPs/blob/main/CAIPs/caip-2.md#syntax}.\n * @returns A CAIP chain ID.\n */\nexport function toCaipChainId(\n  namespace: CaipNamespace,\n  reference: CaipReference,\n): CaipChainId {\n  if (!isCaipNamespace(namespace)) {\n    throw new Error(\n      `Invalid \"namespace\", must match: ${CAIP_NAMESPACE_REGEX.toString()}`,\n    );\n  }\n\n  if (!isCaipReference(reference)) {\n    throw new Error(\n      `Invalid \"reference\", must match: ${CAIP_REFERENCE_REGEX.toString()}`,\n    );\n  }\n\n  return `${namespace}:${reference}`;\n}\n"]}