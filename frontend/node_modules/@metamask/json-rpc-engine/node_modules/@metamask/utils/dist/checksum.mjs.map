{"version": 3, "file": "checksum.mjs", "sourceRoot": "", "sources": ["../src/checksum.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,8BAA8B;AAErD,OAAO,EAAE,MAAM,EAAE,qBAAiB;AAElC,MAAM,CAAC,MAAM,cAAc,GAAG,IAAI,CAChC,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,EAC3C,EAAE,EACF,EAAE,CACH,CAAC", "sourcesContent": ["import { size, string } from '@metamask/superstruct';\n\nimport { base64 } from './base64';\n\nexport const ChecksumStruct = size(\n  base64(string(), { paddingRequired: true }),\n  44,\n  44,\n);\n"]}