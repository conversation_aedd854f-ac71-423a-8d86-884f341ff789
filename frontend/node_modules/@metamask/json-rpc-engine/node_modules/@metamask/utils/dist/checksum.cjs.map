{"version": 3, "file": "checksum.cjs", "sourceRoot": "", "sources": ["../src/checksum.ts"], "names": [], "mappings": ";;;AAAA,uDAAqD;AAErD,yCAAkC;AAErB,QAAA,cAAc,GAAG,IAAA,kBAAI,EAChC,IAAA,eAAM,EAAC,IAAA,oBAAM,GAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,EAC3C,EAAE,EACF,EAAE,CACH,CAAC", "sourcesContent": ["import { size, string } from '@metamask/superstruct';\n\nimport { base64 } from './base64';\n\nexport const ChecksumStruct = size(\n  base64(string(), { paddingRequired: true }),\n  44,\n  44,\n);\n"]}