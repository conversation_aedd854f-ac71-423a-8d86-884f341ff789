{"version": 3, "file": "versions.mjs", "sourceRoot": "", "sources": ["../src/versions.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,8BAA8B;;;AAS3D,OAAO,EAAE,YAAY,EAAE,qBAAiB;AA+CxC;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,MAAM,CACjC,MAAM,EAA4C,EAClD,SAAS,EACT,CAAC,KAAK,EAAE,EAAE;IACR,IAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;QACtC,OAAO,iCAAiC,KAAK,GAAG,CAAC;KAClD;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,MAAM,CACtC,MAAM,EAA0C,EAChD,eAAe,EACf,CAAC,KAAK,EAAE,EAAE;IACR,IAAI,gBAAgB,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;QACpC,OAAO,+BAA+B,KAAK,GAAG,CAAC;KAChD;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CACF,CAAC;AAEF;;;;;GAKG;AACH,MAAM,UAAU,oBAAoB,CAClC,OAAgB;IAEhB,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AACpC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAChC,YAAqB;IAErB,OAAO,EAAE,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;AAC9C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CACnC,OAAgB;IAEhB,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AACvC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CACjC,KAAc;IAEd,YAAY,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,SAAS,CACvB,QAAuB,EACvB,QAAuB;IAEvB,OAAO,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,OAAO,CAAC,OAAsB,EAAE,KAAkB;IAChE,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACnC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,qBAAqB,CACnC,OAAsB,EACtB,YAAyB;IAEzB,OAAO,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE;QAC5C,iBAAiB,EAAE,IAAI;KACxB,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { Struct } from '@metamask/superstruct';\nimport { is, refine, string } from '@metamask/superstruct';\nimport {\n  gt as gtSemver,\n  gtr as gtrSemver,\n  satisfies as satisfiesSemver,\n  valid as validSemVerVersion,\n  validRange as validSemVerRange,\n} from 'semver';\n\nimport { assertStruct } from './assert';\nimport type { Opaque } from './opaque';\n\n/**\n * {@link https://codemix.com/opaque-types-in-javascript/ Opaque} type for SemVer ranges.\n *\n * @example Use {@link assertIsSemVerRange} and {@link isValidSemVerRange} to cast to proper type.\n * ```typescript\n * const unsafeRange: string = dataFromUser();\n * assertIsSemVerRange(unsafeRange);\n * unsafeRange\n * // ^? SemVerRange\n * ```\n * @example If you know what you're doing and want to side-step type safety, casting from a string works correctly.\n * ```typescript\n * const unsafeRange: string = dataFromUser();\n * unsafeRange as SemVerRange;\n * // ^? SemVerRange\n * ```\n * @see {@link assertIsSemVerRange}\n * @see {@link isValidSemVerRange}\n */\nexport type SemVerRange = Opaque<string, typeof semVerRange>;\ndeclare const semVerRange: unique symbol;\n\n/**\n * {@link https://codemix.com/opaque-types-in-javascript/ Opaque} type for singular SemVer version.\n *\n * @example Use {@link assertIsSemVerVersion} and {@link isValidSemVerVersion} to cast to proper type.\n * ```typescript\n * const unsafeVersion: string = dataFromUser();\n * assertIsSemVerVersion(unsafeRange);\n * unsafeVersion\n * // ^? SemVerVersion\n * ```\n * @example If you know what you're doing and want to side-step type safety, casting from a string works correctly.\n * ```typescript\n * const unsafeVersion: string = dataFromUser();\n * unsafeRange as SemVerVersion;\n * // ^? SemVerVersion\n * ```\n * @see {@link assertIsSemVerVersion}\n * @see {@link isValidSemVerVersion}\n */\nexport type SemVerVersion = Opaque<string, typeof semVerVersion>;\ndeclare const semVerVersion: unique symbol;\n\n/**\n * A struct for validating a version string.\n */\nexport const VersionStruct = refine<SemVerVersion, null>(\n  string() as unknown as Struct<SemVerVersion, null>,\n  'Version',\n  (value) => {\n    if (validSemVerVersion(value) === null) {\n      return `Expected SemVer version, got \"${value}\"`;\n    }\n    return true;\n  },\n);\n\nexport const VersionRangeStruct = refine<SemVerRange, null>(\n  string() as unknown as Struct<SemVerRange, null>,\n  'Version range',\n  (value) => {\n    if (validSemVerRange(value) === null) {\n      return `Expected SemVer range, got \"${value}\"`;\n    }\n    return true;\n  },\n);\n\n/**\n * Checks whether a SemVer version is valid.\n *\n * @param version - A potential version.\n * @returns `true` if the version is valid, and `false` otherwise.\n */\nexport function isValidSemVerVersion(\n  version: unknown,\n): version is SemVerVersion {\n  return is(version, VersionStruct);\n}\n\n/**\n * Checks whether a SemVer version range is valid.\n *\n * @param versionRange - A potential version range.\n * @returns `true` if the version range is valid, and `false` otherwise.\n */\nexport function isValidSemVerRange(\n  versionRange: unknown,\n): versionRange is SemVerRange {\n  return is(versionRange, VersionRangeStruct);\n}\n\n/**\n * Asserts that a value is a valid concrete SemVer version.\n *\n * @param version - A potential SemVer concrete version.\n */\nexport function assertIsSemVerVersion(\n  version: unknown,\n): asserts version is SemVerVersion {\n  assertStruct(version, VersionStruct);\n}\n\n/**\n * Asserts that a value is a valid SemVer range.\n *\n * @param range - A potential SemVer range.\n */\nexport function assertIsSemVerRange(\n  range: unknown,\n): asserts range is SemVerRange {\n  assertStruct(range, VersionRangeStruct);\n}\n\n/**\n * Checks whether a SemVer version is greater than another.\n *\n * @param version1 - The left-hand version.\n * @param version2 - The right-hand version.\n * @returns `version1 > version2`.\n */\nexport function gtVersion(\n  version1: SemVerVersion,\n  version2: SemVerVersion,\n): boolean {\n  return gtSemver(version1, version2);\n}\n\n/**\n * Checks whether a SemVer version is greater than all possibilities in a range.\n *\n * @param version - A SemvVer version.\n * @param range - The range to check against.\n * @returns `version > range`.\n */\nexport function gtRange(version: SemVerVersion, range: SemVerRange): boolean {\n  return gtrSemver(version, range);\n}\n\n/**\n * Returns whether a SemVer version satisfies a SemVer range.\n *\n * @param version - The SemVer version to check.\n * @param versionRange - The SemVer version range to check against.\n * @returns Whether the version satisfied the version range.\n */\nexport function satisfiesVersionRange(\n  version: SemVerVersion,\n  versionRange: SemVerRange,\n): boolean {\n  return satisfiesSemver(version, versionRange, {\n    includePrerelease: true,\n  });\n}\n"]}