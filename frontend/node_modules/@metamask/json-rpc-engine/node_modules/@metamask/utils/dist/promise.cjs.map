{"version": 3, "file": "promise.cjs", "sourceRoot": "", "sources": ["../src/promise.ts"], "names": [], "mappings": ";;;AAsBA;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAgB,qBAAqB,CAAgB,EACnD,0BAA0B,GAAG,KAAK,MAGhC,EAAE;IACJ,IAAI,OAA2C,CAAC;IAChD,IAAI,MAAyC,CAAC;IAC9C,MAAM,OAAO,GAAG,IAAI,OAAO,CACzB,CACE,YAAgD,EAChD,WAA8C,EAC9C,EAAE;QACF,OAAO,GAAG,YAAY,CAAC;QACvB,MAAM,GAAG,WAAW,CAAC;IACvB,CAAC,CACF,CAAC;IAEF,IAAI,0BAA0B,EAAE;QAC9B,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;YACvB,uEAAuE;QACzE,CAAC,CAAC,CAAC;KACJ;IAED,2EAA2E;IAC3E,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACtC,CAAC;AAzBD,sDAyBC", "sourcesContent": ["/**\n * A deferred Promise.\n *\n * A deferred Promise is one that can be resolved or rejected independently of\n * the Promise construction.\n * @template Result - The result type of the Promise.\n */\nexport type DeferredPromise<Result = void> = {\n  /**\n   * The Promise that has been deferred.\n   */\n  promise: Promise<Result>;\n  /**\n   * A function that resolves the Promise.\n   */\n  resolve: (result: Result) => void;\n  /**\n   * A function that rejects the Promise.\n   */\n  reject: (error: unknown) => void;\n};\n\n/**\n * Create a defered Promise.\n *\n * If the Promise is rejected prior to a handler being added, this can result in an\n * `UnhandledPromiseRejection` error. Optionally this can be suppressed with the\n * `suppressUnhandledRejection` flag, as it's common to belatedly handle deferred Promises, or to\n * ignore them if they're no longer relevant (e.g. related to a cancelled request).\n *\n * However, be very careful that you have handled the Promise if you do this. Suppressing these\n * errors is dangerous, they exist for good reason. An unhandled rejection can hide errors, making\n * debugging extremely difficult. They should only be suppressed if you're confident that the\n * Promise is always handled correctly, in both the success and failure cases.\n *\n * @param args - The arguments.\n * @param args.suppressUnhandledRejection - This option adds an empty error handler\n * to the Promise to suppress the UnhandledPromiseRejection error. This can be\n * useful if the deferred Promise is sometimes intentionally not used.\n * @returns A deferred Promise.\n * @template Result - The result type of the Promise.\n */\nexport function createDeferredPromise<Result = void>({\n  suppressUnhandledRejection = false,\n}: {\n  suppressUnhandledRejection?: boolean;\n} = {}): DeferredPromise<Result> {\n  let resolve: DeferredPromise<Result>['resolve'];\n  let reject: DeferredPromise<Result>['reject'];\n  const promise = new Promise<Result>(\n    (\n      innerResolve: DeferredPromise<Result>['resolve'],\n      innerReject: DeferredPromise<Result>['reject'],\n    ) => {\n      resolve = innerResolve;\n      reject = innerReject;\n    },\n  );\n\n  if (suppressUnhandledRejection) {\n    promise.catch((_error) => {\n      // This handler is used to suppress the UnhandledPromiseRejection error\n    });\n  }\n\n  // @ts-expect-error We know that these are assigned, but TypeScript doesn't\n  return { promise, resolve, reject };\n}\n"]}