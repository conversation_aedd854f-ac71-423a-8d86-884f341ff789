{"version": 3, "file": "logging.cjs", "sourceRoot": "", "sources": ["../src/logging.ts"], "names": [], "mappings": ";;;;;;AACA,kDAA0B;AAE1B,MAAM,YAAY,GAAG,IAAA,eAAK,EAAC,UAAU,CAAC,CAAC;AAEvC;;;;;;;;;;;GAWG;AACH,SAAgB,mBAAmB,CAAC,WAAmB;IACrD,OAAO,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC1C,CAAC;AAFD,kDAEC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,kBAAkB,CAChC,aAAuB,EACvB,UAAkB;IAElB,OAAO,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1C,CAAC;AALD,gDAKC", "sourcesContent": ["import type { Debugger } from 'debug';\nimport debug from 'debug';\n\nconst globalLogger = debug('metamask');\n\n/**\n * Creates a logger via the `debug` library whose log messages will be tagged\n * using the name of your project. By default, such messages will be\n * suppressed, but you can reveal them by setting the `DEBUG` environment\n * variable to `metamask:<projectName>`. You can also set this variable to\n * `metamask:*` if you want to see log messages from all MetaMask projects that\n * are also using this function to create their loggers.\n *\n * @param projectName - The name of your project. This should be the name of\n * your NPM package if you're developing one.\n * @returns An instance of `debug`.\n */\nexport function createProjectLogger(projectName: string): Debugger {\n  return globalLogger.extend(projectName);\n}\n\n/**\n * Creates a logger via the `debug` library which is derived from the logger for\n * the whole project whose log messages will be tagged using the name of your\n * module. By default, such messages will be suppressed, but you can reveal them\n * by setting the `DEBUG` environment variable to\n * `metamask:<projectName>:<moduleName>`. You can also set this variable to\n * `metamask:<projectName>:*` if you want to see log messages from the project,\n * or `metamask:*` if you want to see log messages from all MetaMask projects.\n *\n * @param projectLogger - The logger created via {@link createProjectLogger}.\n * @param moduleName - The name of your module. You could use the name of the\n * file where you're using this logger or some other name.\n * @returns An instance of `debug`.\n */\nexport function createModuleLogger(\n  projectLogger: Debugger,\n  moduleName: string,\n): Debugger {\n  return projectLogger.extend(moduleName);\n}\n"]}