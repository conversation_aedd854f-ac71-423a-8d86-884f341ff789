{"version": 3, "file": "bytes.cjs", "sourceRoot": "", "sources": ["../src/bytes.ts"], "names": [], "mappings": ";;;AAAA,sCAAqC;AAErC,yCAAkC;AAElC,mCAA2D;AAE3D,2BAA2B;AAC3B,MAAM,4BAA4B,GAAG,EAAE,CAAC;AAExC,2BAA2B;AAC3B,MAAM,4BAA4B,GAAG,EAAE,CAAC;AACxC,MAAM,oBAAoB,GAAG,EAAE,CAAC;AAIhC;;;;;;;;;;;;;GAaG;AACH,SAAS,8BAA8B;IACrC,6EAA6E;IAC7E,6EAA6E;IAC7E,6EAA6E;IAC7E,eAAe;IACf,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,OAAO,GAAG,EAAE;QACV,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;aACnD;SACF;QAED,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,uBAAuB,GAAG,8BAA8B,EAAE,CAAC;AAEjE;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,KAAc;IACpC,OAAO,KAAK,YAAY,UAAU,CAAC;AACrC,CAAC;AAFD,0BAEC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAc;IAC1C,IAAA,eAAM,EAAC,OAAO,CAAC,KAAK,CAAC,EAAE,6BAA6B,CAAC,CAAC;AACxD,CAAC;AAFD,sCAEC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,KAAiB;IAC1C,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,WAAW,GAAG,uBAAuB,EAAE,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,oEAAoE;QACpE,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC;KACzC;IAED,OAAO,IAAA,WAAK,EAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,CAAC;AAhBD,gCAgBC;AAED;;;;;;;;;GASG;AACH,SAAgB,aAAa,CAAC,KAAiB;IAC7C,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IACtC,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;AAC7B,CAAC;AALD,sCAKC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,mBAAmB,CAAC,KAAiB;IACnD,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACtB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,sCAAsC;QACtC,KAAK,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;KAC7C;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC;AAVD,kDAUC;AAED;;;;;;;;GAQG;AACH,SAAgB,aAAa,CAAC,KAAiB;IAC7C,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAEpC,IAAA,eAAM,EACJ,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,EACzC,4DAA4D,CAC7D,CAAC;IAEF,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AAXD,sCAWC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAiB;IAC7C,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAJD,sCAIC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAiB;IAC7C,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,OAAO,aAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAJD,sCAIC;AAED;;;;;;;;GAQG;AACH,SAAgB,UAAU,CAAC,KAAa;IACtC,0CAA0C;IAC1C,IAAI,KAAK,EAAE,WAAW,EAAE,EAAE,KAAK,IAAI,EAAE;QACnC,OAAO,IAAI,UAAU,EAAE,CAAC;KACzB;IAED,IAAA,uBAAiB,EAAC,KAAK,CAAC,CAAC;IAEzB,0EAA0E;IAC1E,wBAAwB;IACxB,MAAM,aAAa,GAAG,IAAA,cAAQ,EAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;IACpD,MAAM,eAAe,GACnB,aAAa,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;IACvE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,2EAA2E;QAC3E,yEAAyE;QACzE,aAAa;QACb,MAAM,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,MAAM,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,EAAE,GACN,EAAE;YACF,CAAC,EAAE,GAAG,4BAA4B;gBAChC,CAAC,CAAC,4BAA4B;gBAC9B,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5B,MAAM,EAAE,GACN,EAAE;YACF,CAAC,EAAE,GAAG,4BAA4B;gBAChC,CAAC,CAAC,4BAA4B;gBAC9B,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAE5B,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;KACzB;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AApCD,gCAoCC;AAED;;;;;;;;GAQG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAA,eAAM,EAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAC7D,IAAA,eAAM,EAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,sCAAsC,CAAC,CAAC;IAEnE,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACvC,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC;AAND,sCAMC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CAAC,KAAa,EAAE,KAAa;IAC9C,IAAA,eAAM,EAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IAElB,+BAA+B;IAC/B,MAAM,IAAI,GAAG,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,8BAA8B;AAChC,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,mBAAmB,CACjC,KAAa,EACb,UAAkB;IAElB,IAAA,eAAM,EAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAC7D,IAAA,eAAM,EAAC,OAAO,UAAU,KAAK,QAAQ,EAAE,+BAA+B,CAAC,CAAC;IACxE,IAAA,eAAM,EAAC,UAAU,GAAG,CAAC,EAAE,qCAAqC,CAAC,CAAC;IAC9D,IAAA,eAAM,EACJ,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAC7B,wDAAwD,CACzD,CAAC;IAEF,0EAA0E;IAC1E,8CAA8C;IAC9C,IAAI,WAAW,GAAG,KAAK,CAAC;IACxB,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;QAClD,sCAAsC;QACtC,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;KAC3B;IAED,OAAO,KAAK,CAAC,OAAO,EAAE,CAAC;AACzB,CAAC;AAxBD,kDAwBC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAA,eAAM,EAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAC7D,IAAA,eAAM,EAAC,KAAK,IAAI,CAAC,EAAE,sCAAsC,CAAC,CAAC;IAC3D,IAAA,eAAM,EACJ,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAC3B,2DAA2D,CAC5D,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACvC,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC;AAVD,sCAUC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAA,eAAM,EAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAE7D,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAJD,sCAIC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAA,eAAM,EAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAE7D,OAAO,aAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAJD,sCAIC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,SAAgB,YAAY,CAAC,KAAY;IACvC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC;KAC7B;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC;KAC7B;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;SAC1B;QAED,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC;KAC7B;IAED,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IAED,MAAM,IAAI,SAAS,CAAC,4BAA4B,OAAO,KAAK,IAAI,CAAC,CAAC;AACpE,CAAC;AAtBD,oCAsBC;AAED;;;;;;;;GAQG;AACH,SAAgB,WAAW,CAAC,MAAe;IACzC,MAAM,gBAAgB,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,oEAAoE;QACpE,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC;QAEvC,gBAAgB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QAC5B,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC;KAC5B;IAED,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5D,gEAAgE;QAChE,uEAAuE;QACvE,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACvC,MAAM,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;KACtC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AArBD,kCAqBC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,cAAc,CAAC,KAAiB;IAC9C,4EAA4E;IAC5E,qEAAqE;IACrE,eAAe;IACf,iDAAiD;IACjD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,KAAK,YAAY,MAAM,EAAE;QAC5D,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAC/B,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CACpC,CAAC;QAEF,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC7B;IAED,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AACxE,CAAC;AAfD,wCAeC", "sourcesContent": ["import { base64 } from '@scure/base';\n\nimport { assert } from './assert';\nimport type { Hex } from './hex';\nimport { add0x, assertIsHexString, remove0x } from './hex';\n\n// '0'.charCodeAt(0) === 48\nconst HEX_MINIMUM_NUMBER_CHARACTER = 48;\n\n// '9'.charCodeAt(0) === 57\nconst HEX_MAXIMUM_NUMBER_CHARACTER = 58;\nconst HEX_CHARACTER_OFFSET = 87;\n\nexport type Bytes = bigint | number | string | Uint8Array;\n\n/**\n * Memoized function that returns an array to be used as a lookup table for\n * converting bytes to hexadecimal values.\n *\n * The array is created lazily and then cached for future use. The benefit of\n * this approach is that the performance of converting bytes to hex is much\n * better than if we were to call `toString(16)` on each byte.\n *\n * The downside is that the array is created once and then never garbage\n * collected. This is not a problem in practice because the array is only 256\n * elements long.\n *\n * @returns A function that returns the lookup table.\n */\nfunction getPrecomputedHexValuesBuilder(): () => string[] {\n  // To avoid issues with tree shaking, we need to use a function to return the\n  // array. This is because the array is only used in the `bytesToHex` function\n  // and if we were to use a global variable, the array might be removed by the\n  // tree shaker.\n  const lookupTable: string[] = [];\n\n  return () => {\n    if (lookupTable.length === 0) {\n      for (let i = 0; i < 256; i++) {\n        lookupTable.push(i.toString(16).padStart(2, '0'));\n      }\n    }\n\n    return lookupTable;\n  };\n}\n\n/**\n * Function implementation of the {@link getPrecomputedHexValuesBuilder}\n * function.\n */\nconst getPrecomputedHexValues = getPrecomputedHexValuesBuilder();\n\n/**\n * Check if a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @returns Whether the value is a `Uint8Array`.\n */\nexport function isBytes(value: unknown): value is Uint8Array {\n  return value instanceof Uint8Array;\n}\n\n/**\n * Assert that a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @throws If the value is not a `Uint8Array`.\n */\nexport function assertIsBytes(value: unknown): asserts value is Uint8Array {\n  assert(isBytes(value), 'Value must be a Uint8Array.');\n}\n\n/**\n * Convert a `Uint8Array` to a hexadecimal string.\n *\n * @param bytes - The bytes to convert to a hexadecimal string.\n * @returns The hexadecimal string.\n */\nexport function bytesToHex(bytes: Uint8Array): Hex {\n  assertIsBytes(bytes);\n\n  if (bytes.length === 0) {\n    return '0x';\n  }\n\n  const lookupTable = getPrecomputedHexValues();\n  const hexadecimal = new Array(bytes.length);\n\n  for (let i = 0; i < bytes.length; i++) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    hexadecimal[i] = lookupTable[bytes[i]!];\n  }\n\n  return add0x(hexadecimal.join(''));\n}\n\n/**\n * Convert a `Uint8Array` to a `bigint`.\n *\n * To convert a `Uint8Array` to a `number` instead, use {@link bytesToNumber}.\n * To convert a two's complement encoded `Uint8Array` to a `bigint`, use\n * {@link bytesToSignedBigInt}.\n *\n * @param bytes - The bytes to convert to a `bigint`.\n * @returns The `bigint`.\n */\nexport function bytesToBigInt(bytes: Uint8Array): bigint {\n  assertIsBytes(bytes);\n\n  const hexadecimal = bytesToHex(bytes);\n  return BigInt(hexadecimal);\n}\n\n/**\n * Convert a `Uint8Array` to a signed `bigint`. This assumes that the bytes are\n * encoded in two's complement.\n *\n * To convert a `Uint8Array` to an unsigned `bigint` instead, use\n * {@link bytesToBigInt}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param bytes - The bytes to convert to a signed `bigint`.\n * @returns The signed `bigint`.\n */\nexport function bytesToSignedBigInt(bytes: Uint8Array): bigint {\n  assertIsBytes(bytes);\n\n  let value = BigInt(0);\n  for (const byte of bytes) {\n    // eslint-disable-next-line no-bitwise\n    value = (value << BigInt(8)) + BigInt(byte);\n  }\n\n  return BigInt.asIntN(bytes.length * 8, value);\n}\n\n/**\n * Convert a `Uint8Array` to a `number`.\n *\n * To convert a `Uint8Array` to a `bigint` instead, use {@link bytesToBigInt}.\n *\n * @param bytes - The bytes to convert to a number.\n * @returns The number.\n * @throws If the resulting number is not a safe integer.\n */\nexport function bytesToNumber(bytes: Uint8Array): number {\n  assertIsBytes(bytes);\n\n  const bigint = bytesToBigInt(bytes);\n\n  assert(\n    bigint <= BigInt(Number.MAX_SAFE_INTEGER),\n    'Number is not a safe integer. Use `bytesToBigInt` instead.',\n  );\n\n  return Number(bigint);\n}\n\n/**\n * Convert a UTF-8 encoded `Uint8Array` to a `string`.\n *\n * @param bytes - The bytes to convert to a string.\n * @returns The string.\n */\nexport function bytesToString(bytes: Uint8Array): string {\n  assertIsBytes(bytes);\n\n  return new TextDecoder().decode(bytes);\n}\n\n/**\n * Convert a `Uint8Array` to a base64 encoded string.\n *\n * @param bytes - The bytes to convert to a base64 encoded string.\n * @returns The base64 encoded string.\n */\nexport function bytesToBase64(bytes: Uint8Array): string {\n  assertIsBytes(bytes);\n\n  return base64.encode(bytes);\n}\n\n/**\n * Convert a hexadecimal string to a `Uint8Array`. The string can optionally be\n * prefixed with `0x`. It accepts even and odd length strings.\n *\n * If the value is \"0x\", an empty `Uint8Array` is returned.\n *\n * @param value - The hexadecimal string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nexport function hexToBytes(value: string): Uint8Array {\n  // \"0x\" is often used as empty byte array.\n  if (value?.toLowerCase?.() === '0x') {\n    return new Uint8Array();\n  }\n\n  assertIsHexString(value);\n\n  // Remove the `0x` prefix if it exists, and pad the string to have an even\n  // number of characters.\n  const strippedValue = remove0x(value).toLowerCase();\n  const normalizedValue =\n    strippedValue.length % 2 === 0 ? strippedValue : `0${strippedValue}`;\n  const bytes = new Uint8Array(normalizedValue.length / 2);\n\n  for (let i = 0; i < bytes.length; i++) {\n    // While this is not the prettiest way to convert a hexadecimal string to a\n    // `Uint8Array`, it is a lot faster than using `parseInt` to convert each\n    // character.\n    const c1 = normalizedValue.charCodeAt(i * 2);\n    const c2 = normalizedValue.charCodeAt(i * 2 + 1);\n    const n1 =\n      c1 -\n      (c1 < HEX_MAXIMUM_NUMBER_CHARACTER\n        ? HEX_MINIMUM_NUMBER_CHARACTER\n        : HEX_CHARACTER_OFFSET);\n    const n2 =\n      c2 -\n      (c2 < HEX_MAXIMUM_NUMBER_CHARACTER\n        ? HEX_MINIMUM_NUMBER_CHARACTER\n        : HEX_CHARACTER_OFFSET);\n\n    bytes[i] = n1 * 16 + n2;\n  }\n\n  return bytes;\n}\n\n/**\n * Convert a `bigint` to a `Uint8Array`.\n *\n * This assumes that the `bigint` is an unsigned integer. To convert a signed\n * `bigint` instead, use {@link signedBigIntToBytes}.\n *\n * @param value - The bigint to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nexport function bigIntToBytes(value: bigint): Uint8Array {\n  assert(typeof value === 'bigint', 'Value must be a bigint.');\n  assert(value >= BigInt(0), 'Value must be a non-negative bigint.');\n\n  const hexadecimal = value.toString(16);\n  return hexToBytes(hexadecimal);\n}\n\n/**\n * Check if a `bigint` fits in a certain number of bytes.\n *\n * @param value - The `bigint` to check.\n * @param bytes - The number of bytes.\n * @returns Whether the `bigint` fits in the number of bytes.\n */\nfunction bigIntFits(value: bigint, bytes: number): boolean {\n  assert(bytes > 0);\n\n  /* eslint-disable no-bitwise */\n  const mask = value >> BigInt(31);\n  return !(((~value & mask) + (value & ~mask)) >> BigInt(bytes * 8 + ~0));\n  /* eslint-enable no-bitwise */\n}\n\n/**\n * Convert a signed `bigint` to a `Uint8Array`. This uses two's complement\n * encoding to represent negative numbers.\n *\n * To convert an unsigned `bigint` to a `Uint8Array` instead, use\n * {@link bigIntToBytes}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param value - The number to convert to bytes.\n * @param byteLength - The length of the resulting `Uint8Array`. If the number\n * is larger than the maximum value that can be represented by the given length,\n * an error is thrown.\n * @returns The bytes as `Uint8Array`.\n */\nexport function signedBigIntToBytes(\n  value: bigint,\n  byteLength: number,\n): Uint8Array {\n  assert(typeof value === 'bigint', 'Value must be a bigint.');\n  assert(typeof byteLength === 'number', 'Byte length must be a number.');\n  assert(byteLength > 0, 'Byte length must be greater than 0.');\n  assert(\n    bigIntFits(value, byteLength),\n    'Byte length is too small to represent the given value.',\n  );\n\n  // ESLint doesn't like mutating function parameters, so to avoid having to\n  // disable the rule, we create a new variable.\n  let numberValue = value;\n  const bytes = new Uint8Array(byteLength);\n\n  for (let i = 0; i < bytes.length; i++) {\n    bytes[i] = Number(BigInt.asUintN(8, numberValue));\n    // eslint-disable-next-line no-bitwise\n    numberValue >>= BigInt(8);\n  }\n\n  return bytes.reverse();\n}\n\n/**\n * Convert a `number` to a `Uint8Array`.\n *\n * @param value - The number to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n * @throws If the number is not a safe integer.\n */\nexport function numberToBytes(value: number): Uint8Array {\n  assert(typeof value === 'number', 'Value must be a number.');\n  assert(value >= 0, 'Value must be a non-negative number.');\n  assert(\n    Number.isSafeInteger(value),\n    'Value is not a safe integer. Use `bigIntToBytes` instead.',\n  );\n\n  const hexadecimal = value.toString(16);\n  return hexToBytes(hexadecimal);\n}\n\n/**\n * Convert a `string` to a UTF-8 encoded `Uint8Array`.\n *\n * @param value - The string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nexport function stringToBytes(value: string): Uint8Array {\n  assert(typeof value === 'string', 'Value must be a string.');\n\n  return new TextEncoder().encode(value);\n}\n\n/**\n * Convert a base64 encoded string to a `Uint8Array`.\n *\n * @param value - The base64 encoded string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nexport function base64ToBytes(value: string): Uint8Array {\n  assert(typeof value === 'string', 'Value must be a string.');\n\n  return base64.decode(value);\n}\n\n/**\n * Convert a byte-like value to a `Uint8Array`. The value can be a `Uint8Array`,\n * a `bigint`, a `number`, or a `string`.\n *\n * This will attempt to guess the type of the value based on its type and\n * contents. For more control over the conversion, use the more specific\n * conversion functions, such as {@link hexToBytes} or {@link stringToBytes}.\n *\n * If the value is a `string`, and it is prefixed with `0x`, it will be\n * interpreted as a hexadecimal string. Otherwise, it will be interpreted as a\n * UTF-8 string. To convert a hexadecimal string to bytes without interpreting\n * it as a UTF-8 string, use {@link hexToBytes} instead.\n *\n * If the value is a `bigint`, it is assumed to be unsigned. To convert a signed\n * `bigint` to bytes, use {@link signedBigIntToBytes} instead.\n *\n * If the value is a `Uint8Array`, it will be returned as-is.\n *\n * @param value - The value to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nexport function valueToBytes(value: Bytes): Uint8Array {\n  if (typeof value === 'bigint') {\n    return bigIntToBytes(value);\n  }\n\n  if (typeof value === 'number') {\n    return numberToBytes(value);\n  }\n\n  if (typeof value === 'string') {\n    if (value.startsWith('0x')) {\n      return hexToBytes(value);\n    }\n\n    return stringToBytes(value);\n  }\n\n  if (isBytes(value)) {\n    return value;\n  }\n\n  throw new TypeError(`Unsupported value type: \"${typeof value}\".`);\n}\n\n/**\n * Concatenate multiple byte-like values into a single `Uint8Array`. The values\n * can be `Uint8Array`, `bigint`, `number`, or `string`. This uses\n * {@link valueToBytes} under the hood to convert each value to bytes. Refer to\n * the documentation of that function for more information.\n *\n * @param values - The values to concatenate.\n * @returns The concatenated bytes as `Uint8Array`.\n */\nexport function concatBytes(values: Bytes[]): Uint8Array {\n  const normalizedValues = new Array(values.length);\n  let byteLength = 0;\n\n  for (let i = 0; i < values.length; i++) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const value = valueToBytes(values[i]!);\n\n    normalizedValues[i] = value;\n    byteLength += value.length;\n  }\n\n  const bytes = new Uint8Array(byteLength);\n  for (let i = 0, offset = 0; i < normalizedValues.length; i++) {\n    // While we could simply spread the values into an array and use\n    // `Uint8Array.from`, that is a lot slower than using `Uint8Array.set`.\n    bytes.set(normalizedValues[i], offset);\n    offset += normalizedValues[i].length;\n  }\n\n  return bytes;\n}\n\n/**\n * Create a {@link DataView} from a {@link Uint8Array}. This is a convenience\n * function that avoids having to create a {@link DataView} manually, which\n * requires passing the `byteOffset` and `byteLength` parameters every time.\n *\n * Not passing the `byteOffset` and `byteLength` parameters can result in\n * unexpected behavior when the {@link Uint8Array} is a view of a larger\n * {@link ArrayBuffer}, e.g., when using {@link Uint8Array.subarray}.\n *\n * This function also supports Node.js {@link Buffer}s.\n *\n * @example\n * ```typescript\n * const bytes = new Uint8Array([1, 2, 3]);\n *\n * // This is equivalent to:\n * // const dataView = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n * const dataView = createDataView(bytes);\n * ```\n * @param bytes - The bytes to create the {@link DataView} from.\n * @returns The {@link DataView}.\n */\nexport function createDataView(bytes: Uint8Array): DataView {\n  // To maintain compatibility with Node.js, we need to check if the bytes are\n  // a Buffer. If so, we need to slice the buffer to get the underlying\n  // ArrayBuffer.\n  // eslint-disable-next-line no-restricted-globals\n  if (typeof Buffer !== 'undefined' && bytes instanceof Buffer) {\n    const buffer = bytes.buffer.slice(\n      bytes.byteOffset,\n      bytes.byteOffset + bytes.byteLength,\n    );\n\n    return new DataView(buffer);\n  }\n\n  return new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n}\n"]}