import type { Struct } from "@metamask/superstruct";
export type Base64Options = {
    /**
     * Is the `=` padding at the end required or not.
     *
     * @default false
     */
    paddingRequired?: boolean;
    /**
     * Which character set should be used.
     * The sets are based on {@link https://datatracker.ietf.org/doc/html/rfc4648 RFC 4648}.
     *
     * @default 'base64'
     */
    characterSet?: 'base64' | 'base64url';
};
/**
 * Ensure that a provided string-based struct is valid base64.
 *
 * @param struct - The string based struct.
 * @param options - Optional options to specialize base64 validation. See {@link Base64Options} documentation.
 * @returns A superstruct validating base64.
 */
export declare const base64: <Type extends string, Schema>(struct: Struct<Type, Schema>, options?: Base64Options) => Struct<Type, Schema>;
//# sourceMappingURL=base64.d.mts.map