{"version": 3, "file": "number.mjs", "sourceRoot": "", "sources": ["../src/number.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,qBAAiB;AAClC,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,kBAAc;AAEjD;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAU,EAAE;IACnD,MAAM,CAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAC7D,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,sCAAsC,CAAC,CAAC;IAC3D,MAAM,CACJ,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAC3B,yDAAyD,CAC1D,CAAC;IAEF,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAU,EAAE;IACnD,MAAM,CAAC,OAAO,KAAK,KAAK,QAAQ,EAAE,yBAAyB,CAAC,CAAC;IAC7D,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,sCAAsC,CAAC,CAAC;IAE3D,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAU,EAAE;IACnD,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAEzB,2EAA2E;IAC3E,kEAAkE;IAClE,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAExC,MAAM,CACJ,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,EACjC,yDAAyD,CAC1D,CAAC;IAEF,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAU,EAAE;IACnD,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAEzB,2EAA2E;IAC3E,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC", "sourcesContent": ["import { assert } from './assert';\nimport { add0x, assertIsHexString } from './hex';\n\n/**\n * Convert a number to a hexadecimal string. This verifies that the number is a\n * non-negative safe integer.\n *\n * To convert a `bigint` to a hexadecimal string instead, use\n * {@link bigIntToHex}.\n *\n * @example\n * ```typescript\n * numberToHex(0); // '0x0'\n * numberToHex(1); // '0x1'\n * numberToHex(16); // '0x10'\n * ```\n * @param value - The number to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the number is not a non-negative safe integer.\n */\nexport const numberToHex = (value: number): string => {\n  assert(typeof value === 'number', 'Value must be a number.');\n  assert(value >= 0, 'Value must be a non-negative number.');\n  assert(\n    Number.isSafeInteger(value),\n    'Value is not a safe integer. Use `bigIntToHex` instead.',\n  );\n\n  return add0x(value.toString(16));\n};\n\n/**\n * Convert a `bigint` to a hexadecimal string. This verifies that the `bigint`\n * is a non-negative integer.\n *\n * To convert a number to a hexadecimal string instead, use {@link numberToHex}.\n *\n * @example\n * ```typescript\n * bigIntToHex(0n); // '0x0'\n * bigIntToHex(1n); // '0x1'\n * bigIntToHex(16n); // '0x10'\n * ```\n * @param value - The `bigint` to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the `bigint` is not a non-negative integer.\n */\nexport const bigIntToHex = (value: bigint): string => {\n  assert(typeof value === 'bigint', 'Value must be a bigint.');\n  assert(value >= 0, 'Value must be a non-negative bigint.');\n\n  return add0x(value.toString(16));\n};\n\n/**\n * Convert a hexadecimal string to a number. This verifies that the string is a\n * valid hex string, and that the resulting number is a safe integer. Both\n * \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a `bigint` instead, use\n * {@link hexToBigInt}.\n *\n * @example\n * ```typescript\n * hexToNumber('0x0'); // 0\n * hexToNumber('0x1'); // 1\n * hexToNumber('0x10'); // 16\n * ```\n * @param value - The hexadecimal string to convert to a number.\n * @returns The number.\n * @throws If the value is not a valid hexadecimal string, or if the resulting\n * number is not a safe integer.\n */\nexport const hexToNumber = (value: string): number => {\n  assertIsHexString(value);\n\n  // `parseInt` accepts values without the \"0x\"-prefix, whereas `Number` does\n  // not. Using this is slightly faster than `Number(add0x(value))`.\n  const numberValue = parseInt(value, 16);\n\n  assert(\n    Number.isSafeInteger(numberValue),\n    'Value is not a safe integer. Use `hexToBigInt` instead.',\n  );\n\n  return numberValue;\n};\n\n/**\n * Convert a hexadecimal string to a `bigint`. This verifies that the string is\n * a valid hex string. Both \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a number instead, use {@link hexToNumber}.\n *\n * @example\n * ```typescript\n * hexToBigInt('0x0'); // 0n\n * hexToBigInt('0x1'); // 1n\n * hexToBigInt('0x10'); // 16n\n * ```\n * @param value - The hexadecimal string to convert to a `bigint`.\n * @returns The `bigint`.\n * @throws If the value is not a valid hexadecimal string.\n */\nexport const hexToBigInt = (value: string): bigint => {\n  assertIsHexString(value);\n\n  // The `BigInt` constructor requires the \"0x\"-prefix to parse a hex string.\n  return BigInt(add0x(value));\n};\n"]}