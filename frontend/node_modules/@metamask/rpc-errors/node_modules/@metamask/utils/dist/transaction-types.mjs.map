{"version": 3, "file": "transaction-types.mjs", "sourceRoot": "", "sources": ["../src/transaction-types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Bytes } from './bytes';\nimport type { Hex } from './hex';\n\nexport type Transaction =\n  | LegacyTransaction\n  | EIP2930Transaction\n  | EIP1559Transaction;\n\nexport type SignedTransaction = Transaction & Signature;\n\nexport type Signature = {\n  /**\n   * EC signature parameter\n   * 32 bytes long sequence.\n   */\n  r: Bytes;\n\n  /**\n   * EC signature parameter\n   * Signature proof.\n   * 32 bytes long sequence\n   */\n  s: Bytes;\n\n  /**\n   * Recovery identifier. It can be either 0x1b or 0x1c\n   * 1 byte long sequence\n   */\n  yParity: Bytes;\n};\n\n/**\n * Base Ethereum Transaction\n */\nexport type BaseTransaction = {\n  /**\n   * Sequentially incrementing counter which indicates the transaction\n   * number from the account\n   */\n  nonce: Bytes;\n\n  /**\n   * The address of the sender, that will be signing the transaction\n   */\n  from: Hex | Uint8Array;\n\n  /**\n   * The receiving address.\n   * If an externally-owned account, the transaction will transfer value.\n   * If a contract account, the transaction will execute the contract code.\n   */\n  to: Hex | Uint8Array;\n\n  /**\n   * The amount of Ether sent.\n   */\n  value: Bytes;\n\n  /**\n   * Maximum amount of gas units that this transaction can consume.\n   */\n  gasLimit: Bytes;\n\n  /**\n   * Arbitrary data.\n   */\n  data?: Bytes;\n};\n\n/**\n * Typed Ethereum Transaction\n */\nexport type TypedTransaction = BaseTransaction & {\n  /**\n   * Transaction type.\n   */\n  type: number;\n};\n\n/**\n * Ethereum Legacy Transaction\n * Reference: https://ethereum.org/en/developers/docs/transactions/\n */\nexport type LegacyTransaction = BaseTransaction & {\n  /**\n   * Transaction's gas price.\n   */\n  gasPrice: Bytes | null;\n};\n\n/**\n * EIP-2930 Transaction: Optional Access Lists\n * Reference: https://eips.ethereum.org/EIPS/eip-2930\n */\nexport type EIP2930Transaction = TypedTransaction & {\n  /**\n   * Transaction type.\n   */\n  type: 1;\n\n  /**\n   * Transaction chain ID\n   */\n  chainId: Bytes;\n\n  /**\n   * List of addresses and storage keys that the transaction plans to access\n   */\n  accessList:\n    | { address: Hex; storageKeys: Hex[] }[]\n    | { address: Uint8Array; storageKeys: Uint8Array[] }[];\n};\n\n/**\n * EIP-1559 Transaction: Fee market change for ETH 1.0 chain (Type-2)\n *\n * Reference: https://eips.ethereum.org/EIPS/eip-1559\n */\nexport type EIP1559Transaction = TypedTransaction & {\n  /**\n   * Transaction type.\n   */\n  type: 2;\n\n  /**\n   * Maximum fee to give to the miner\n   */\n  maxPriorityFeePerGas: Bytes;\n\n  /**\n   * Maximum total fee\n   */\n  maxFeePerGas: Bytes;\n};\n"]}