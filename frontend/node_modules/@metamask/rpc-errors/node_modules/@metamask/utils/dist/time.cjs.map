{"version": 3, "file": "time.cjs", "sourceRoot": "", "sources": ["../src/time.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,IAAY,QAmCX;AAnCD,WAAY,QAAQ;IAClB;;OAEG;IACH,qDAAe,CAAA;IAEf;;OAEG;IACH,8CAAa,CAAA;IAEb;;OAEG;IACH,+CAAe,CAAA;IAEf;;OAEG;IACH,6CAAgB,CAAA;IAEhB;;OAEG;IACH,4CAAgB,CAAA;IAEhB;;OAEG;IACH,+CAAkB,CAAA;IAElB;;OAEG;IACH,iDAAqB,CAAA;AACvB,CAAC,EAnCW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAmCnB;AAED,MAAM,oBAAoB,GAAG,CAAC,MAAc,EAAE,EAAE,CAC9C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AAE1C,MAAM,0BAA0B,GAAG,CAAC,MAAc,EAAE,IAAY,EAAE,EAAE;IAClE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CACb,IAAI,IAAI,gDAAgD,MAAM,IAAI,CACnE,CAAC;KACH;AACH,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,KAAa,EAAE,QAAkB;IAC9D,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3C,OAAO,KAAK,GAAG,QAAQ,CAAC;AAC1B,CAAC;AAHD,wCAGC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,SAAiB;IACzC,0BAA0B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AAChC,CAAC;AAHD,8BAGC", "sourcesContent": ["/**\n * Common duration constants, in milliseconds.\n */\nexport enum Duration {\n  /**\n   * A millisecond.\n   */\n  Millisecond = 1,\n\n  /**\n   * A second, in milliseconds.\n   */\n  Second = 1000, // Millisecond * 1000\n\n  /**\n   * A minute, in milliseconds.\n   */\n  Minute = 60_000, // Second * 60\n\n  /**\n   * An hour, in milliseconds.\n   */\n  Hour = 3_600_000, // Minute * 60\n\n  /**\n   * A day, in milliseconds.\n   */\n  Day = 86_400_000, // Hour * 24\n\n  /**\n   * A week, in milliseconds.\n   */\n  Week = 604_800_000, // Day * 7\n\n  /**\n   * A year, in milliseconds.\n   */\n  Year = 31_536_000_000, // Day * 365\n}\n\nconst isNonNegativeInteger = (number: number) =>\n  Number.isInteger(number) && number >= 0;\n\nconst assertIsNonNegativeInteger = (number: number, name: string) => {\n  if (!isNonNegativeInteger(number)) {\n    throw new Error(\n      `\"${name}\" must be a non-negative integer. Received: \"${number}\".`,\n    );\n  }\n};\n\n/**\n * Calculates the millisecond value of the specified number of units of time.\n *\n * @param count - The number of units of time.\n * @param duration - The unit of time to count.\n * @returns The count multiplied by the specified duration.\n */\nexport function inMilliseconds(count: number, duration: Duration): number {\n  assertIsNonNegativeInteger(count, 'count');\n  return count * duration;\n}\n\n/**\n * Gets the milliseconds since a particular Unix epoch timestamp.\n *\n * @param timestamp - A Unix millisecond timestamp.\n * @returns The number of milliseconds elapsed since the specified timestamp.\n */\nexport function timeSince(timestamp: number): number {\n  assertIsNonNegativeInteger(timestamp, 'timestamp');\n  return Date.now() - timestamp;\n}\n"]}