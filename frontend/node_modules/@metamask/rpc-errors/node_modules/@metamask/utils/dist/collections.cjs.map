{"version": 3, "file": "collections.cjs", "sourceRoot": "", "sources": ["../src/collections.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;GAKG;AACH,MAAM,SAAS;IAGb,IAAW,IAAI;QACb,OAAO,uBAAA,IAAI,sBAAK,CAAC,IAAI,CAAC;IACxB,CAAC;IAEM,kCAAC,MAAM,CAAC,QAAQ,EAAC;QACtB,OAAO,uBAAA,IAAI,sBAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IACtC,CAAC;IAED,YAAY,OAAmD;QAVtD,iCAAsB;QAW7B,uBAAA,IAAI,kBAAQ,IAAI,GAAG,CAAa,OAAO,CAAC,MAAA,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAEM,OAAO;QACZ,OAAO,uBAAA,IAAI,sBAAK,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEM,OAAO,CACZ,UAAuD,EACvD,OAAa;QAEb,qEAAqE;QACrE,0CAA0C;QAC1C,OAAO,uBAAA,IAAI,sBAAK,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,GAAQ,EAAE,IAAa,EAAE,EAAE,CACjE,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAC3C,CAAC;IACJ,CAAC;IAEM,GAAG,CAAC,GAAQ;QACjB,OAAO,uBAAA,IAAI,sBAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEM,GAAG,CAAC,GAAQ;QACjB,OAAO,uBAAA,IAAI,sBAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEM,IAAI;QACT,OAAO,uBAAA,IAAI,sBAAK,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEM,MAAM;QACX,OAAO,uBAAA,IAAI,sBAAK,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAEM,QAAQ;QACb,OAAO,aAAa,IAAI,CAAC,IAAI,MAC3B,IAAI,CAAC,IAAI,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;iBACpB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;iBAC3D,IAAI,CAAC,IAAI,CAAC,GAAG;YAClB,CAAC,CAAC,EACN,GAAG,CAAC;IACN,CAAC;CACF;AAkEQ,8BAAS;AAhElB;;;;;GAKG;AACH,MAAM,SAAS;IAGb,IAAW,IAAI;QACb,OAAO,uBAAA,IAAI,sBAAK,CAAC,IAAI,CAAC;IACxB,CAAC;IAEM,kCAAC,MAAM,CAAC,QAAQ,EAAC;QACtB,OAAO,uBAAA,IAAI,sBAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IACtC,CAAC;IAED,YAAY,MAAgC;QAVnC,iCAAiB;QAWxB,uBAAA,IAAI,kBAAQ,IAAI,GAAG,CAAQ,MAAM,CAAC,MAAA,CAAC;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAEM,OAAO;QACZ,OAAO,uBAAA,IAAI,sBAAK,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEM,OAAO,CACZ,UAA4D,EAC5D,OAAa;QAEb,qEAAqE;QACrE,0CAA0C;QAC1C,OAAO,uBAAA,IAAI,sBAAK,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,MAAa,EAAE,IAAa,EAAE,EAAE,CACtE,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAC9C,CAAC;IACJ,CAAC;IAEM,GAAG,CAAC,KAAY;QACrB,OAAO,uBAAA,IAAI,sBAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEM,IAAI;QACT,OAAO,uBAAA,IAAI,sBAAK,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEM,MAAM;QACX,OAAO,uBAAA,IAAI,sBAAK,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAEM,QAAQ;QACb,OAAO,aAAa,IAAI,CAAC,IAAI,MAC3B,IAAI,CAAC,IAAI,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;YACtE,CAAC,CAAC,EACN,GAAG,CAAC;IACN,CAAC;CACF;AAQmB,8BAAS;AAN7B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAEnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC", "sourcesContent": ["/**\n * A {@link ReadonlyMap} that cannot be modified after instantiation.\n * The implementation uses an inner map hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this map.\n */\nclass FrozenMap<Key, Value> implements ReadonlyMap<Key, Value> {\n  readonly #map: Map<Key, Value>;\n\n  public get size() {\n    return this.#map.size;\n  }\n\n  public [Symbol.iterator]() {\n    return this.#map[Symbol.iterator]();\n  }\n\n  constructor(entries?: readonly (readonly [Key, Value])[] | null) {\n    this.#map = new Map<Key, Value>(entries);\n    Object.freeze(this);\n  }\n\n  public entries() {\n    return this.#map.entries();\n  }\n\n  public forEach(\n    callbackfn: (value: Value, key: Key, map: this) => void,\n    thisArg?: any,\n  ): void {\n    // We have to wrap the specified callback in order to prevent it from\n    // receiving a reference to the inner map.\n    return this.#map.forEach((value: Value, key: Key, _map: unknown) =>\n      callbackfn.call(thisArg, value, key, this),\n    );\n  }\n\n  public get(key: Key) {\n    return this.#map.get(key);\n  }\n\n  public has(key: Key) {\n    return this.#map.has(key);\n  }\n\n  public keys() {\n    return this.#map.keys();\n  }\n\n  public values() {\n    return this.#map.values();\n  }\n\n  public toString(): string {\n    return `FrozenMap(${this.size}) {${\n      this.size > 0\n        ? ` ${[...this.entries()]\n            .map(([key, value]) => `${String(key)} => ${String(value)}`)\n            .join(', ')} `\n        : ''\n    }}`;\n  }\n}\n\n/**\n * A {@link ReadonlySet} that cannot be modified after instantiation.\n * The implementation uses an inner set hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this set.\n */\nclass FrozenSet<Value> implements ReadonlySet<Value> {\n  readonly #set: Set<Value>;\n\n  public get size() {\n    return this.#set.size;\n  }\n\n  public [Symbol.iterator]() {\n    return this.#set[Symbol.iterator]();\n  }\n\n  constructor(values?: readonly Value[] | null) {\n    this.#set = new Set<Value>(values);\n    Object.freeze(this);\n  }\n\n  public entries() {\n    return this.#set.entries();\n  }\n\n  public forEach(\n    callbackfn: (value: Value, value2: Value, set: this) => void,\n    thisArg?: any,\n  ): void {\n    // We have to wrap the specified callback in order to prevent it from\n    // receiving a reference to the inner set.\n    return this.#set.forEach((value: Value, value2: Value, _set: unknown) =>\n      callbackfn.call(thisArg, value, value2, this),\n    );\n  }\n\n  public has(value: Value) {\n    return this.#set.has(value);\n  }\n\n  public keys() {\n    return this.#set.keys();\n  }\n\n  public values() {\n    return this.#set.values();\n  }\n\n  public toString(): string {\n    return `FrozenSet(${this.size}) {${\n      this.size > 0\n        ? ` ${[...this.values()].map((member) => String(member)).join(', ')} `\n        : ''\n    }}`;\n  }\n}\n\nObject.freeze(FrozenMap);\nObject.freeze(FrozenMap.prototype);\n\nObject.freeze(FrozenSet);\nObject.freeze(FrozenSet.prototype);\n\nexport { FrozenMap, FrozenSet };\n"]}