{"version": 3, "file": "caip-types.d.cts", "sourceRoot": "", "sources": ["../src/caip-types.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,8BAA8B;AAE3D,eAAO,MAAM,mBAAmB,QACqC,CAAC;AAEtE,eAAO,MAAM,oBAAoB,QAAsB,CAAC;AAExD,eAAO,MAAM,oBAAoB,QAA2B,CAAC;AAE7D,eAAO,MAAM,qBAAqB,QACwF,CAAC;AAE3H,eAAO,MAAM,0BAA0B,QAA6B,CAAC;AAErE,eAAO,MAAM,qBAAqB,QAC2H,CAAC;AAE9J,eAAO,MAAM,mBAAmB,QAC+J,CAAC;AAEhM;;GAEG;AACH,eAAO,MAAM,iBAAiB,qCAGA,CAAC;AAC/B,MAAM,MAAM,WAAW,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;AAEhD;;GAEG;AACH,eAAO,MAAM,mBAAmB,sBAA0C,CAAC;AAC3E,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,mBAAmB,CAAC,CAAC;AAE9D;;GAEG;AACH,eAAO,MAAM,mBAAmB,sBAA0C,CAAC;AAC3E,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,mBAAmB,CAAC,CAAC;AAE9D;;GAEG;AACH,eAAO,MAAM,mBAAmB,+CAGA,CAAC;AACjC,MAAM,MAAM,aAAa,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;AAE5D;;GAEG;AACH,eAAO,MAAM,wBAAwB,sBAGpC,CAAC;AACF,MAAM,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,wBAAwB,CAAC,CAAC;AAExE;;GAEG;AACH,eAAO,MAAM,mBAAmB,yDAGA,CAAC;AACjC,MAAM,MAAM,aAAa,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;AAEtE;;GAEG;AACH,eAAO,MAAM,iBAAiB,mEAGA,CAAC;AAC/B,MAAM,MAAM,WAAW,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;AAE9E,6BAA6B;AAC7B,oBAAY,kBAAkB;IAC5B,2CAA2C;IAC3C,MAAM,WAAW;IACjB,iCAAiC;IACjC,MAAM,WAAW;IACjB,MAAM,WAAW;CAClB;AAED;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,WAAW,CAElE;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAEtE;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAEtE;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAEtE;AAED;;;;;GAKG;AACH,wBAAgB,oBAAoB,CAClC,KAAK,EAAE,OAAO,GACb,KAAK,IAAI,kBAAkB,CAE7B;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAEtE;AAED;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,WAAW,CAElE;AAED;;;;;;GAMG;AACH,wBAAgB,gBAAgB,CAAC,WAAW,EAAE,WAAW,GAAG;IAC1D,SAAS,EAAE,aAAa,CAAC;IACzB,SAAS,EAAE,aAAa,CAAC;CAC1B,CAUA;AAED;;;;;;GAMG;AACH,wBAAgB,kBAAkB,CAAC,aAAa,EAAE,aAAa,GAAG;IAChE,OAAO,EAAE,kBAAkB,CAAC;IAC5B,OAAO,EAAE,WAAW,CAAC;IACrB,KAAK,EAAE;QAAE,SAAS,EAAE,aAAa,CAAC;QAAC,SAAS,EAAE,aAAa,CAAA;KAAE,CAAC;CAC/D,CAcA;AAED;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,aAAa,CAC3B,SAAS,EAAE,aAAa,EACxB,SAAS,EAAE,aAAa,GACvB,WAAW,CAcb"}