{"version": 3, "file": "coercers.cjs", "sourceRoot": "", "sources": ["../src/coercers.ts"], "names": [], "mappings": ";;;AACA,uDAS+B;AAE/B,yCAAkC;AAClC,uCAAiD;AAEjD,mCAAwC;AAExC,MAAM,gBAAgB,GAAG,IAAA,mBAAK,EAAC,CAAC,IAAA,oBAAM,GAAE,EAAE,IAAA,oBAAM,GAAE,EAAE,IAAA,oBAAM,GAAE,EAAE,qBAAe,CAAC,CAAC,CAAC;AAChF,MAAM,aAAa,GAAG,IAAA,oBAAM,EAAC,IAAA,oBAAM,GAAE,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;AACjE,MAAM,aAAa,GAAG,IAAA,oBAAM,EAAC,IAAA,oBAAM,GAAE,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;AAEjE,MAAM,eAAe,GAAG,IAAA,mBAAK,EAAC,CAAC,qBAAe,EAAE,IAAA,sBAAQ,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvE,MAAM,YAAY,GAAG,IAAA,oBAAM,EACzB,IAAA,sBAAQ,EAAC,UAAU,CAAC,EACpB,IAAA,mBAAK,EAAC,CAAC,qBAAe,CAAC,CAAC,EACxB,kBAAU,CACX,CAAC;AAEF,MAAM,UAAU,GAAG,IAAA,oBAAM,EAAC,qBAAe,EAAE,IAAA,sBAAQ,EAAC,UAAU,CAAC,EAAE,kBAAU,CAAC,CAAC;AAK7E;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,SAAgB,YAAY,CAAC,KAAiB;IAC5C,IAAI;QACF,MAAM,MAAM,GAAG,IAAA,oBAAM,EAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAE5C,IAAA,eAAM,EACJ,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EACvB,sCAAsC,KAAK,IAAI,CAChD,CAAC;QAEF,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,yBAAW,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,IAAI,CAAC,CAAC;SAClE;QAED,0BAA0B;QAC1B,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAlBD,oCAkBC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,YAAY,CAAC,KAAiB;IAC5C,IAAI;QACF,2EAA2E;QAC3E,mDAAmD;QACnD,OAAO,IAAA,oBAAM,EAAC,KAAK,EAAE,aAAa,CAAC,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,yBAAW,EAAE;YAChC,MAAM,IAAI,KAAK,CACb,sCAAsC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAC9D,CAAC;SACH;QAED,0BAA0B;QAC1B,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAfD,oCAeC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,WAAW,CAAC,KAAgB;IAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC7D,OAAO,IAAI,UAAU,EAAE,CAAC;KACzB;IAED,IAAI;QACF,OAAO,IAAA,oBAAM,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;KACpC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,yBAAW,EAAE;YAChC,MAAM,IAAI,KAAK,CACb,qCAAqC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAC7D,CAAC;SACH;QAED,0BAA0B;QAC1B,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAjBD,kCAiBC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,SAAS,CAAC,KAAgB;IACxC,IACE,CAAC,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;QACnD,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,EAC3D;QACA,OAAO,IAAI,CAAC;KACb;IAED,IAAI;QACF,OAAO,IAAA,oBAAM,EAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,yBAAW,EAAE;YAChC,MAAM,IAAI,KAAK,CACb,qCAAqC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAC7D,CAAC;SACH;QAED,0BAA0B;QAC1B,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AApBD,8BAoBC", "sourcesContent": ["import type { Infer } from '@metamask/superstruct';\nimport {\n  bigint,\n  coerce,\n  create,\n  instance,\n  number,\n  string,\n  StructError,\n  union,\n} from '@metamask/superstruct';\n\nimport { assert } from './assert';\nimport { bytesToHex, hexToBytes } from './bytes';\nimport type { Hex } from './hex';\nimport { StrictHexStruct } from './hex';\n\nconst NumberLikeStruct = union([number(), bigint(), string(), StrictHexStruct]);\nconst NumberCoercer = coerce(number(), NumberLikeStruct, Number);\nconst BigIntCoercer = coerce(bigint(), NumberLikeStruct, BigInt);\n\nconst BytesLikeStruct = union([StrictHexStruct, instance(Uint8Array)]);\nconst BytesCoercer = coerce(\n  instance(Uint8Array),\n  union([StrictHexStruct]),\n  hexToBytes,\n);\n\nconst HexCoercer = coerce(StrictHexStruct, instance(Uint8Array), bytesToHex);\n\nexport type NumberLike = Infer<typeof NumberLikeStruct>;\nexport type BytesLike = Infer<typeof BytesLikeStruct>;\n\n/**\n * Create a number from a number-like value.\n *\n * - If the value is a number, it is returned as-is.\n * - If the value is a `bigint`, it is converted to a number.\n * - If the value is a string, it is interpreted as a decimal number.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number.\n *\n * This validates that the value is a number-like value, and that the resulting\n * number is not `NaN` or `Infinity`.\n *\n * @example\n * ```typescript\n * const value = createNumber('0x010203');\n * console.log(value); // 66051\n *\n * const otherValue = createNumber(123n);\n * console.log(otherValue); // 123\n * ```\n * @param value - The value to create the number from.\n * @returns The created number.\n * @throws If the value is not a number-like value, or if the resulting number\n * is `NaN` or `Infinity`.\n */\nexport function createNumber(value: NumberLike): number {\n  try {\n    const result = create(value, NumberCoercer);\n\n    assert(\n      Number.isFinite(result),\n      `Expected a number-like value, got \"${value}\".`,\n    );\n\n    return result;\n  } catch (error) {\n    if (error instanceof StructError) {\n      throw new Error(`Expected a number-like value, got \"${value}\".`);\n    }\n\n    /* istanbul ignore next */\n    throw error;\n  }\n}\n\n/**\n * Create a `bigint` from a number-like value.\n *\n * - If the value is a number, it is converted to a `bigint`.\n * - If the value is a `bigint`, it is returned as-is.\n * - If the value is a string, it is interpreted as a decimal number and\n * converted to a `bigint`.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number and converted to a `bigint`.\n *\n * @example\n * ```typescript\n * const value = createBigInt('0x010203');\n * console.log(value); // 16909060n\n *\n * const otherValue = createBigInt(123);\n * console.log(otherValue); // 123n\n * ```\n * @param value - The value to create the bigint from.\n * @returns The created bigint.\n * @throws If the value is not a number-like value.\n */\nexport function createBigInt(value: NumberLike): bigint {\n  try {\n    // The `BigInt` constructor throws if the value is not a number-like value.\n    // There is no need to validate the value manually.\n    return create(value, BigIntCoercer);\n  } catch (error) {\n    if (error instanceof StructError) {\n      throw new Error(\n        `Expected a number-like value, got \"${String(error.value)}\".`,\n      );\n    }\n\n    /* istanbul ignore next */\n    throw error;\n  }\n}\n\n/**\n * Create a byte array from a bytes-like value.\n *\n * - If the value is a byte array, it is returned as-is.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is interpreted\n * as a hexadecimal number and converted to a byte array.\n *\n * @example\n * ```typescript\n * const value = createBytes('0x010203');\n * console.log(value); // Uint8Array [ 1, 2, 3 ]\n *\n * const otherValue = createBytes('0x010203');\n * console.log(otherValue); // Uint8Array [ 1, 2, 3 ]\n * ```\n * @param value - The value to create the byte array from.\n * @returns The created byte array.\n * @throws If the value is not a bytes-like value.\n */\nexport function createBytes(value: BytesLike): Uint8Array {\n  if (typeof value === 'string' && value.toLowerCase() === '0x') {\n    return new Uint8Array();\n  }\n\n  try {\n    return create(value, BytesCoercer);\n  } catch (error) {\n    if (error instanceof StructError) {\n      throw new Error(\n        `Expected a bytes-like value, got \"${String(error.value)}\".`,\n      );\n    }\n\n    /* istanbul ignore next */\n    throw error;\n  }\n}\n\n/**\n * Create a hexadecimal string from a bytes-like value.\n *\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is returned\n * as-is.\n * - If the value is a `Uint8Array`, it is converted to a hex string.\n *\n * @example\n * ```typescript\n * const value = createHex(new Uint8Array([1, 2, 3]));\n * console.log(value); // '0x010203'\n *\n * const otherValue = createHex('0x010203');\n * console.log(otherValue); // '0x010203'\n * ```\n * @param value - The value to create the hex string from.\n * @returns The created hex string.\n * @throws If the value is not a bytes-like value.\n */\nexport function createHex(value: BytesLike): Hex {\n  if (\n    (value instanceof Uint8Array && value.length === 0) ||\n    (typeof value === 'string' && value.toLowerCase() === '0x')\n  ) {\n    return '0x';\n  }\n\n  try {\n    return create(value, HexCoercer);\n  } catch (error) {\n    if (error instanceof StructError) {\n      throw new Error(\n        `Expected a bytes-like value, got \"${String(error.value)}\".`,\n      );\n    }\n\n    /* istanbul ignore next */\n    throw error;\n  }\n}\n"]}