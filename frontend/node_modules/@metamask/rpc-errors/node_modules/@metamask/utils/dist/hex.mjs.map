{"version": 3, "file": "hex.mjs", "sourceRoot": "", "sources": ["../src/hex.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,8BAA8B;AAC5D,OAAO,EAAE,UAAU,IAAI,SAAS,EAAE,2BAA2B;AAE7D,OAAO,EAAE,MAAM,EAAE,qBAAiB;AAClC,OAAO,EAAE,UAAU,EAAE,oBAAgB;AAIrC,MAAM,CAAC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,sBAAsB,CAAC,CAAC;AACnE,MAAM,CAAC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,iBAAiB,CAGjE,CAAC;AACF,MAAM,CAAC,MAAM,gBAAgB,GAAG,OAAO,CACrC,MAAM,EAAE,EACR,mBAAmB,CACC,CAAC;AACvB,MAAM,CAAC,MAAM,wBAAwB,GAAG,OAAO,CAC7C,MAAM,EAAE,EACR,sBAAsB,CACF,CAAC;AAEvB;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;AACpC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,qCAAqC,CAAC,CAAC;AACpE,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,uBAAuB,CAAC,KAAc;IACpD,MAAM,CACJ,iBAAiB,CAAC,KAAK,CAAC,EACxB,yDAAyD,CAC1D,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAAC,eAAoB;IACpD,OAAO,CACL,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;QACrC,sBAAsB,CAAC,eAAe,CAAC,CACxC,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAY;IAC7C,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,wBAAwB,CAAC,EAAE,sBAAsB,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IACnD,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACnE,OAAO,KAAK,UAAU;SACnB,KAAK,CAAC,EAAE,CAAC;SACT,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE;QAC9B,MAAM,aAAa,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC;QAClE,OAAO,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;YACpC,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE;YACzB,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,sBAAsB,CAAC,gBAAqB;IAC1D,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,EAAE;QACnD,OAAO,KAAK,CAAC;KACd;IAED,OAAO,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,gBAAgB,CAAC;AACnE,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,KAAK,CAAC,WAAmB;IACvC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,WAAkB,CAAC;KAC3B;IAED,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,KAAK,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;KACxC;IAED,OAAO,KAAK,WAAW,EAAE,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,WAAmB;IAC1C,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAChE,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACjC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["import type { Struct } from '@metamask/superstruct';\nimport { is, pattern, string } from '@metamask/superstruct';\nimport { keccak_256 as keccak256 } from '@noble/hashes/sha3';\n\nimport { assert } from './assert';\nimport { bytesToHex } from './bytes';\n\nexport type Hex = `0x${string}`;\n\nexport const HexStruct = pattern(string(), /^(?:0x)?[0-9a-f]+$/iu);\nexport const StrictHexStruct = pattern(string(), /^0x[0-9a-f]+$/iu) as Struct<\n  Hex,\n  null\n>;\nexport const HexAddressStruct = pattern(\n  string(),\n  /^0x[0-9a-f]{40}$/u,\n) as Struct<Hex, null>;\nexport const HexChecksumAddressStruct = pattern(\n  string(),\n  /^0x[0-9a-fA-F]{40}$/u,\n) as Struct<Hex, null>;\n\n/**\n * Check if a string is a valid hex string.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nexport function isHexString(value: unknown): value is string {\n  return is(value, HexStruct);\n}\n\n/**\n * Strictly check if a string is a valid hex string. A valid hex string must\n * start with the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nexport function isStrictHexString(value: unknown): value is Hex {\n  return is(value, StrictHexStruct);\n}\n\n/**\n * Assert that a value is a valid hex string.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nexport function assertIsHexString(value: unknown): asserts value is string {\n  assert(isHexString(value), 'Value must be a hexadecimal string.');\n}\n\n/**\n * Assert that a value is a valid hex string. A valid hex string must start with\n * the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nexport function assertIsStrictHexString(value: unknown): asserts value is Hex {\n  assert(\n    isStrictHexString(value),\n    'Value must be a hexadecimal string, starting with \"0x\".',\n  );\n}\n\n/**\n * Validate that the passed prefixed hex string is an all-lowercase\n * hex address, or a valid mixed-case checksum address.\n *\n * @param possibleAddress - Input parameter to check against.\n * @returns Whether or not the input is a valid hex address.\n */\nexport function isValidHexAddress(possibleAddress: Hex) {\n  return (\n    is(possibleAddress, HexAddressStruct) ||\n    isValidChecksumAddress(possibleAddress)\n  );\n}\n\n/**\n * Encode a passed hex string as an ERC-55 mixed-case checksum address.\n *\n * @param address - The hex address to encode.\n * @returns The address encoded according to ERC-55.\n * @see https://eips.ethereum.org/EIPS/eip-55\n */\nexport function getChecksumAddress(address: Hex): Hex {\n  assert(is(address, HexChecksumAddressStruct), 'Invalid hex address.');\n  const unPrefixed = remove0x(address.toLowerCase());\n  const unPrefixedHash = remove0x(bytesToHex(keccak256(unPrefixed)));\n  return `0x${unPrefixed\n    .split('')\n    .map((character, nibbleIndex) => {\n      const hashCharacter = unPrefixedHash[nibbleIndex];\n      assert(is(hashCharacter, string()), 'Hash shorter than address.');\n      return parseInt(hashCharacter, 16) > 7\n        ? character.toUpperCase()\n        : character;\n    })\n    .join('')}`;\n}\n\n/**\n * Validate that the passed hex string is a valid ERC-55 mixed-case\n * checksum address.\n *\n * @param possibleChecksum - The hex address to check.\n * @returns True if the address is a checksum address.\n */\nexport function isValidChecksumAddress(possibleChecksum: Hex) {\n  if (!is(possibleChecksum, HexChecksumAddressStruct)) {\n    return false;\n  }\n\n  return getChecksumAddress(possibleChecksum) === possibleChecksum;\n}\n\n/**\n * Add the `0x`-prefix to a hexadecimal string. If the string already has the\n * prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to add the prefix to.\n * @returns The prefixed hexadecimal string.\n */\nexport function add0x(hexadecimal: string): Hex {\n  if (hexadecimal.startsWith('0x')) {\n    return hexadecimal as Hex;\n  }\n\n  if (hexadecimal.startsWith('0X')) {\n    return `0x${hexadecimal.substring(2)}`;\n  }\n\n  return `0x${hexadecimal}`;\n}\n\n/**\n * Remove the `0x`-prefix from a hexadecimal string. If the string doesn't have\n * the prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to remove the prefix from.\n * @returns The un-prefixed hexadecimal string.\n */\nexport function remove0x(hexadecimal: string): string {\n  if (hexadecimal.startsWith('0x') || hexadecimal.startsWith('0X')) {\n    return hexadecimal.substring(2);\n  }\n\n  return hexadecimal;\n}\n"]}