{"version": 3, "file": "errors.cjs", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": ";;;AAAA,2CAA4C;AAE5C,qCAAqD;AAErD;;;;;;;GAOG;AACH,SAAS,OAAO,CAAC,KAAc;IAC7B,OAAO,CACL,KAAK,YAAY,KAAK;QACtB,CAAC,IAAA,eAAQ,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,OAAO,CAAC,CACxD,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AACxE,CAAC;AAFD,0CAEC;AAED;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAChC,KAAc;IAEd,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC;AAC3E,CAAC;AAJD,gDAIC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,CAAC;AACzE,CAAC;AAFD,4CAEC;AAED;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,IAAI,kBAAkB,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE;QAClE,OAAO,KAAK,CAAC,OAAO,CAAC;KACtB;IAED,IAAI,IAAA,wBAAiB,EAAC,KAAK,CAAC,EAAE;QAC5B,OAAO,EAAE,CAAC;KACX;IAED,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAVD,0CAUC;AAED;;;;;;;;;;;;GAYG;AACH,SAAgB,SAAS,CACvB,aAAwB,EACxB,OAAe;IAEf,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI,KAAgC,CAAC;QACrC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,yDAAyD;YACzD,qEAAqE;YACrE,sHAAsH;YACtH,6DAA6D;YAC7D,aAAa;YACb,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;SACtD;aAAM;YACL,6DAA6D;YAC7D,aAAa;YACb,KAAK,GAAG,IAAI,2BAAc,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,IAAI,eAAe,CAAC,aAAa,CAAC,EAAE;YAClC,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;SACjC;QAED,OAAO,KAAK,CAAC;KACd;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC;KAC1D;IAED,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAC1C,CAAC;AA/BD,8BA+BC", "sourcesContent": ["import { Error<PERSON><PERSON><PERSON>ause } from 'pony-cause';\n\nimport { isNullOrUndefined, isObject } from './misc';\n\n/**\n * Type guard for determining whether the given value is an instance of <PERSON>rror.\n * For errors generated via `fs.promises`, `error instanceof Error` won't work,\n * so we have to come up with another way of testing.\n *\n * @param error - The object to check.\n * @returns A boolean.\n */\nfunction isError(error: unknown): error is Error {\n  return (\n    error instanceof Error ||\n    (isObject(error) && error.constructor.name === 'Error')\n  );\n}\n\n/**\n * Type guard for determining whether the given value is an error object with a\n * `code` property such as the type of error that <PERSON><PERSON> throws for filesystem\n * operations, etc.\n *\n * @param error - The object to check.\n * @returns A boolean.\n */\nexport function isErrorWithCode(error: unknown): error is { code: string } {\n  return typeof error === 'object' && error !== null && 'code' in error;\n}\n\n/**\n * Type guard for determining whether the given value is an error object with a\n * `message` property, such as an instance of <PERSON><PERSON><PERSON>.\n *\n * @param error - The object to check.\n * @returns A boolean.\n */\nexport function isErrorWithMessage(\n  error: unknown,\n): error is { message: string } {\n  return typeof error === 'object' && error !== null && 'message' in error;\n}\n\n/**\n * Type guard for determining whether the given value is an error object with a\n * `stack` property, such as an instance of Error.\n *\n * @param error - The object to check.\n * @returns A boolean.\n */\nexport function isErrorWithStack(error: unknown): error is { stack: string } {\n  return typeof error === 'object' && error !== null && 'stack' in error;\n}\n\n/**\n * Attempts to obtain the message from a possible error object, defaulting to an\n * empty string if it is impossible to do so.\n *\n * @param error - The possible error to get the message from.\n * @returns The message if `error` is an object with a `message` property;\n * the string version of `error` if it is not `undefined` or `null`; otherwise\n * an empty string.\n */\nexport function getErrorMessage(error: unknown): string {\n  if (isErrorWithMessage(error) && typeof error.message === 'string') {\n    return error.message;\n  }\n\n  if (isNullOrUndefined(error)) {\n    return '';\n  }\n\n  return String(error);\n}\n\n/**\n * Builds a new error object, linking it to the original error via the `cause`\n * property if it is an Error.\n *\n * This function is useful to reframe error messages in general, but is\n * _critical_ when interacting with any of Node's filesystem functions as\n * provided via `fs.promises`, because these do not produce stack traces in the\n * case of an I/O error (see <https://github.com/nodejs/node/issues/30944>).\n *\n * @param originalError - The error to be wrapped (something throwable).\n * @param message - The desired message of the new error.\n * @returns A new error object.\n */\nexport function wrapError<Throwable>(\n  originalError: Throwable,\n  message: string,\n): Error & { code?: string } {\n  if (isError(originalError)) {\n    let error: Error & { code?: string };\n    if (Error.length === 2) {\n      // for some reason `tsserver` is not complaining that the\n      // Error constructor doesn't support a second argument in the editor,\n      // but `tsc` does. Error causes are not supported by our current tsc target (ES2020, we need ES2022 to make this work)\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      error = new Error(message, { cause: originalError });\n    } else {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      error = new ErrorWithCause(message, { cause: originalError });\n    }\n\n    if (isErrorWithCode(originalError)) {\n      error.code = originalError.code;\n    }\n\n    return error;\n  }\n\n  if (message.length > 0) {\n    return new Error(`${String(originalError)}: ${message}`);\n  }\n\n  return new Error(String(originalError));\n}\n"]}