{"version": 3, "file": "base64.cjs", "sourceRoot": "", "sources": ["../src/base64.ts"], "names": [], "mappings": ";;;AACA,uDAAgD;AAEhD,yCAAkC;AAmBlC;;;;;;GAMG;AACI,MAAM,MAAM,GAAG,CACpB,MAA4B,EAC5B,UAAyB,EAAE,EAC3B,EAAE;IACF,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;IACzD,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC;IAEtD,IAAI,OAAe,CAAC;IACpB,IAAI,YAAY,KAAK,QAAQ,EAAE;QAC7B,OAAO,GAAG,MAAM,CAAC,GAAG,CAAA,gBAAgB,CAAC;KACtC;SAAM;QACL,IAAA,eAAM,EAAC,YAAY,KAAK,WAAW,CAAC,CAAC;QACrC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAA,eAAe,CAAC;KACrC;IAED,IAAI,EAAU,CAAC;IACf,IAAI,eAAe,EAAE;QACnB,EAAE,GAAG,IAAI,MAAM,CACb,OAAO,OAAO,WAAW,OAAO,QAAQ,OAAO,UAAU,EACzD,GAAG,CACJ,CAAC;KACH;SAAM;QACL,EAAE,GAAG,IAAI,MAAM,CACb,OAAO,OAAO,WAAW,OAAO,SAAS,OAAO,QAAQ,OAAO,UAAU,EACzE,GAAG,CACJ,CAAC;KACH;IAED,OAAO,IAAA,qBAAO,EAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC7B,CAAC,CAAC;AA7BW,QAAA,MAAM,UA6BjB", "sourcesContent": ["import type { Struct } from '@metamask/superstruct';\nimport { pattern } from '@metamask/superstruct';\n\nimport { assert } from './assert';\n\nexport type Base64Options = {\n  /**\n   * Is the `=` padding at the end required or not.\n   *\n   * @default false\n   */\n  // Padding is optional in RFC 4648, that's why the default value is false\n  paddingRequired?: boolean;\n  /**\n   * Which character set should be used.\n   * The sets are based on {@link https://datatracker.ietf.org/doc/html/rfc4648 RFC 4648}.\n   *\n   * @default 'base64'\n   */\n  characterSet?: 'base64' | 'base64url';\n};\n\n/**\n * Ensure that a provided string-based struct is valid base64.\n *\n * @param struct - The string based struct.\n * @param options - Optional options to specialize base64 validation. See {@link Base64Options} documentation.\n * @returns A superstruct validating base64.\n */\nexport const base64 = <Type extends string, Schema>(\n  struct: Struct<Type, Schema>,\n  options: Base64Options = {},\n) => {\n  const paddingRequired = options.paddingRequired ?? false;\n  const characterSet = options.characterSet ?? 'base64';\n\n  let letters: string;\n  if (characterSet === 'base64') {\n    letters = String.raw`[A-Za-z0-9+\\/]`;\n  } else {\n    assert(characterSet === 'base64url');\n    letters = String.raw`[-_A-Za-z0-9]`;\n  }\n\n  let re: RegExp;\n  if (paddingRequired) {\n    re = new RegExp(\n      `^(?:${letters}{4})*(?:${letters}{3}=|${letters}{2}==)?$`,\n      'u',\n    );\n  } else {\n    re = new RegExp(\n      `^(?:${letters}{4})*(?:${letters}{2,3}|${letters}{3}=|${letters}{2}==)?$`,\n      'u',\n    );\n  }\n\n  return pattern(struct, re);\n};\n"]}