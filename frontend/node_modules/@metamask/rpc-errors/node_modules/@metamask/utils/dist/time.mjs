/**
 * Common duration constants, in milliseconds.
 */
export var Duration;
(function (Duration) {
    /**
     * A millisecond.
     */
    Duration[Duration["Millisecond"] = 1] = "Millisecond";
    /**
     * A second, in milliseconds.
     */
    Duration[Duration["Second"] = 1000] = "Second";
    /**
     * A minute, in milliseconds.
     */
    Duration[Duration["Minute"] = 60000] = "Minute";
    /**
     * An hour, in milliseconds.
     */
    Duration[Duration["Hour"] = 3600000] = "Hour";
    /**
     * A day, in milliseconds.
     */
    Duration[Duration["Day"] = 86400000] = "Day";
    /**
     * A week, in milliseconds.
     */
    Duration[Duration["Week"] = 604800000] = "Week";
    /**
     * A year, in milliseconds.
     */
    Duration[Duration["Year"] = 31536000000] = "Year";
})(Duration = Duration || (Duration = {}));
const isNonNegativeInteger = (number) => Number.isInteger(number) && number >= 0;
const assertIsNonNegativeInteger = (number, name) => {
    if (!isNonNegativeInteger(number)) {
        throw new Error(`"${name}" must be a non-negative integer. Received: "${number}".`);
    }
};
/**
 * Calculates the millisecond value of the specified number of units of time.
 *
 * @param count - The number of units of time.
 * @param duration - The unit of time to count.
 * @returns The count multiplied by the specified duration.
 */
export function inMilliseconds(count, duration) {
    assertIsNonNegativeInteger(count, 'count');
    return count * duration;
}
/**
 * Gets the milliseconds since a particular Unix epoch timestamp.
 *
 * @param timestamp - A Unix millisecond timestamp.
 * @returns The number of milliseconds elapsed since the specified timestamp.
 */
export function timeSince(timestamp) {
    assertIsNonNegativeInteger(timestamp, 'timestamp');
    return Date.now() - timestamp;
}
//# sourceMappingURL=time.mjs.map