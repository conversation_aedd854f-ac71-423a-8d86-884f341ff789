{"version": 3, "file": "keyring.mjs", "sourceRoot": "", "sources": ["../src/keyring.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { TypedTransaction, TxData } from '@ethereumjs/tx';\n\nimport type { Eip1024EncryptedData } from './encryption-types';\nimport type { Hex } from './hex';\nimport type { <PERSON><PERSON> } from './json';\n\n/**\n * A Keyring class.\n *\n * This type is used to validate the constructor signature and the `type`\n * static property on Keyring classes. See the {@link Keyring} type for more\n * information.\n */\nexport type KeyringClass<State extends Json> = {\n  /**\n   * The Keyring constructor. Takes a single parameter, an \"options\" object.\n   * See the documentation for the specific keyring for more information about\n   * what these options are.\n   *\n   * @param options - The constructor options. Differs between keyring\n   * implementations.\n   */\n  new (options?: Record<string, unknown>): Keyring<State>;\n\n  /**\n   * The name of this type of keyring. This must uniquely identify the\n   * keyring type.\n   */\n  type: string;\n};\n\n/**\n * A keyring is something that can sign messages. Keyrings are used to add new\n * signing strategies; each strategy is a new keyring.\n *\n * Each keyring manages a collection of key pairs, which we call \"accounts\".\n * Each account is referred to by its \"address\", which is a unique identifier\n * derived from the public key. The address is always a \"0x\"-prefixed\n * hexidecimal string.\n *\n * The keyring might store the private key for each account as well, but it's\n * not guaranteed. Some keyrings delegate signing, so they don't need the\n * private key directly. The keyring (and in particular the keyring state)\n * should be treated with care though, just in case it does contain sensitive\n * material such as a private key.\n */\nexport type Keyring<State extends Json> = {\n  /**\n   * The name of this type of keyring. This must match the `type` property of\n   * the keyring class.\n   */\n  type: string;\n\n  /**\n   * Get the addresses for all accounts in this keyring.\n   *\n   * @returns A list of the account addresses for this keyring\n   */\n  getAccounts(): Promise<Hex[]>;\n\n  /**\n   * Add an account to the keyring.\n   *\n   * @param number - The number of accounts to add. Usually defaults to 1.\n   * @returns A list of the newly added account addresses.\n   */\n  addAccounts(number: number): Promise<Hex[]>;\n\n  /**\n   * Serialize the keyring state as a JSON-serializable object.\n   *\n   * @returns A JSON-serializable representation of the keyring state.\n   */\n  serialize(): Promise<State>;\n\n  /**\n   * Deserialize the given keyring state, overwriting any existing state with\n   * the serialized state provided.\n   *\n   * @param state - A JSON-serializable representation of the keyring state.\n   */\n  deserialize(state: State): Promise<void>;\n\n  /**\n   * Method to include asynchronous configuration.\n   */\n  init?(): Promise<void>;\n\n  /**\n   * Remove an account from the keyring.\n   *\n   * @param address - The address of the account to remove.\n   */\n  removeAccount?(address: Hex): void;\n\n  /**\n   * Export the private key for one of the keyring accounts.\n   *\n   * Some keyrings accept an \"options\" parameter as well. See the documentation\n   * for the specific keyring for more information about what these options\n   * are. For some keyrings, the options parameter is used to allow exporting a\n   * private key that is derived from the given account, rather than exporting\n   * that account's private key directly.\n   *\n   * @param address - The address of the account to export.\n   * @param options - Export options; differs between keyrings.\n   * @returns The non-prefixed, hex-encoded private key that was requested.\n   */\n  exportAccount?(\n    address: Hex,\n    options?: Record<string, unknown>,\n  ): Promise<string>;\n\n  /**\n   * Get the \"app key\" address for the given account and origin. An app key is\n   * an application-specific key pair. See {@link https://eips.ethereum.org/EIPS/eip-1775|EIP-1775}\n   * for more information. The {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Origin|origin}\n   * is used as the unique identifier for the application, and it's used as\n   * part of the key derivation process.\n   *\n   * @param address - The address of the account the app key is derived from.\n   * @param origin - The origin of the application.\n   * @returns The address of the app key for the given account and origin.\n   */\n  getAppKeyAddress?(address: Hex, origin: string): Promise<Hex>;\n\n  /**\n   * Sign a transaction. This is equivalent to the `eth_signTransaction`\n   * Ethereum JSON-RPC method. See the Ethereum JSON-RPC API documentation for\n   * more details.\n   *\n   * Some keyrings accept an \"options\" parameter as well. See the documentation\n   * for the specific keyring for more information about what these options\n   * are. For some keyrings, the options parameter can even change which key is\n   * used for signing (e.g. signing with app keys).\n   *\n   * @param address - The address of the account to use for signing.\n   * @param transaction - The transaction to sign.\n   * @param options - Signing options; differs between keyrings.\n   * @returns The signed transaction.\n   */\n  signTransaction?(\n    address: Hex,\n    transaction: TypedTransaction,\n    options?: Record<string, unknown>,\n  ): Promise<TxData>;\n\n  /**\n   * Sign a message. This is equivalent to an older version of the the\n   * `eth_sign` Ethereum JSON-RPC method. The message is signed using ECDSA,\n   * using the curve secp256k1 the Keccak-256 hash function.\n   *\n   * For more information about this method and why we still support it, see\n   * the {@link https://docs.metamask.io/guide/signing-data.html|MetaMask Docs}.\n   *\n   * Some keyrings accept an \"options\" parameter as well. See the documentation\n   * for the specific keyring for more information about what these options\n   * are. For some keyrings, the options parameter can even change which key is\n   * used for signing (e.g. signing with app keys).\n   *\n   * @param address - The address of the account to use for signing.\n   * @param message - The message to sign.\n   * @param options - Signing options; differs between keyrings.\n   * @returns The signed message.\n   */\n  signMessage?(\n    address: Hex,\n    message: string,\n    options?: Record<string, unknown>,\n  ): Promise<string>;\n\n  /**\n   * Sign a message. This is equivalent to the `eth_sign` Ethereum JSON-RPC\n   * method, which is exposed by MetaMask as the method `personal_sign`. See\n   * the Ethereum JSON-RPC API documentation for more details.\n   *\n   * For more information about this method and why we call it `personal_sign`,\n   * see the {@link https://docs.metamask.io/guide/signing-data.html|MetaMask Docs}.\n   *\n   * Some keyrings accept an \"options\" parameter as well. See the documentation\n   * for the specific keyring for more information about what these options\n   * are. For some keyrings, the options parameter can even change which key is\n   * used for signing (e.g. signing with app keys).\n   *\n   * @param address - The address of the account to use for signing.\n   * @param message - The message to sign.\n   * @param options - Signing options; differs between keyrings.\n   * @returns The signed message.\n   */\n  signPersonalMessage?(\n    address: Hex,\n    message: Hex,\n    options?: { version?: string } & Record<string, unknown>,\n  ): Promise<string>;\n\n  /**\n   * Sign a message. This is equivalent to the `eth_signTypedData` Ethereum\n   * JSON-RPC method. See {@link https://github.com/ethereum/EIPs/blob/master/EIPS/eip-712.md|EIP-712}\n   * for more details.\n   *\n   * The \"version\" option dictates which version of `eth_signTypedData` is\n   * used. The latest version reflects the specification most closely, whereas\n   * earlier versions reflect earlier drafts of the specification that are\n   * still supported for backwards-compatibility reasons. For more information\n   * about why we support multiple versions, see the {@link https://docs.metamask.io/guide/signing-data.html|MetaMask Docs}.\n   *\n   * Some keyrings accept additional options as well. See the documentation for\n   * the specific keyring for more information about what these options are.\n   * For some keyrings, the options parameter can even change which key is used\n   * for signing (e.g. signing with app keys).\n   *\n   * @param address - The address of the account to use for signing.\n   * @param typedData - The data to sign.\n   * @param options - Signing options; differs between keyrings.\n   * @returns The signed message.\n   */\n  signTypedData?(\n    address: Hex,\n    typedData: Record<string, unknown>,\n    options?: Record<string, unknown>,\n  ): Promise<string>;\n\n  /**\n   * Get a public key to use for encryption. This is equivalent to the\n   * ` eth_getEncryptionPublicKey` JSON-RPC method. See the {@link https://docs.metamask.io/guide/rpc-api.html#eth-getencryptionpublickey|MetaMask Docs}\n   * for more information.\n   *\n   * Some keyrings accept an \"options\" parameter as well. See the documentation\n   * for the specific keyring for more information about what these options\n   * are. For some keyrings, the options parameter can even change which key is\n   * used (e.g. encrypting with app keys).\n   *\n   * @param account - The address of the account you want the encryption key for.\n   * @param options - Options; differs between keyrings.\n   */\n  getEncryptionPublicKey?(\n    account: Hex,\n    options?: Record<string, unknown>,\n  ): Promise<string>;\n\n  /**\n   * Decrypt an encrypted message. This is equivalent to the `  eth_decrypt`\n   * JSON-RPC method. See the {@link https://docs.metamask.io/guide/rpc-api.html#eth-decrypt|MetaMask Docs}\n   * for more information.\n   *\n   * @param account - The address of the account you want to use to decrypt\n   * the message.\n   * @param encryptedData - The encrypted data that you want to decrypt.\n   * @returns The decrypted data.\n   */\n  decryptMessage?(\n    account: Hex,\n    encryptedData: Eip1024EncryptedData,\n  ): Promise<string>;\n\n  /**\n   * Generates the properties for the keyring based on the given\n   * BIP39-compliant mnemonic.\n   */\n  generateRandomMnemonic?(): void;\n\n  /**\n   * Destroy the keyring.\n   */\n  destroy?(): Promise<void>;\n};\n"]}