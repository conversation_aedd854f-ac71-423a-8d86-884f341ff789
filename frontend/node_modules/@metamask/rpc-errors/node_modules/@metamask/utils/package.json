{"name": "@metamask/utils", "version": "9.3.0", "description": "Various JavaScript/TypeScript utilities of wide relevance to the MetaMask codebase", "homepage": "https://github.com/MetaMask/utils#readme", "bugs": {"url": "https://github.com/MetaMask/utils/issues"}, "repository": {"type": "git", "url": "https://github.com/MetaMask/utils.git"}, "license": "ISC", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./node": {"import": {"types": "./dist/node.d.mts", "default": "./dist/node.mjs"}, "require": {"types": "./dist/node.d.cts", "default": "./dist/node.cjs"}}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "files": ["dist"], "scripts": {"build": "ts-bridge --project tsconfig.build.json --clean", "build:docs": "typedoc", "lint": "yarn lint:eslint && yarn lint:constraints && yarn lint:misc --check && yarn lint:dependencies --check && yarn lint:changelog", "lint:changelog": "auto-changelog validate", "lint:constraints": "yarn constraints", "lint:dependencies": "depcheck && yarn dedupe", "lint:eslint": "eslint . --cache --ext js,ts", "lint:fix": "yarn lint:eslint --fix && yarn lint:constraints --fix && yarn lint:misc --write && yarn lint:dependencies && yarn lint:changelog", "lint:misc": "prettier '**/*.json' '**/*.md' '!CHANGELOG.md' '**/*.yml' '!.yarnrc.yml' --ignore-path .gitignore --no-error-on-unmatched-pattern", "prepack": "./scripts/prepack.sh", "test": "yarn test:source && yarn test:types", "test:source": "jest && jest-it-up", "test:types": "tsd", "test:watch": "jest --watch"}, "resolutions": {"jest-worker@^28.1.3": "patch:jest-worker@npm%3A28.1.3#./.yarn/patches/jest-worker-npm-28.1.3-5d0ff9006c.patch"}, "dependencies": {"@ethereumjs/tx": "^4.2.0", "@metamask/superstruct": "^3.1.0", "@noble/hashes": "^1.3.1", "@scure/base": "^1.1.3", "@types/debug": "^4.1.7", "debug": "^4.3.4", "pony-cause": "^2.1.10", "semver": "^7.5.4", "uuid": "^9.0.1"}, "devDependencies": {"@lavamoat/allow-scripts": "^3.0.4", "@lavamoat/preinstall-always-fail": "^1.0.0", "@metamask/auto-changelog": "^3.1.0", "@metamask/eslint-config": "^12.0.0", "@metamask/eslint-config-jest": "^12.0.0", "@metamask/eslint-config-nodejs": "^12.0.0", "@metamask/eslint-config-typescript": "^12.0.0", "@ts-bridge/cli": "^0.1.2", "@ts-bridge/shims": "^0.1.1", "@types/jest": "^28.1.7", "@types/jest-when": "^3.5.3", "@types/node": "^20.12.7", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "depcheck": "^1.4.7", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "eslint-plugin-jsdoc": "^39.9.1", "eslint-plugin-n": "^15.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.2.2", "jest-it-up": "^2.0.2", "jest-when": "^3.6.0", "prettier": "^2.7.1", "prettier-plugin-packagejson": "^2.3.0", "stdio-mock": "^1.2.0", "ts-jest": "^29.0.3", "ts-node": "^10.7.0", "tsd": "^0.29.0", "typedoc": "^0.23.15", "typescript": "~5.0.4"}, "packageManager": "yarn@3.2.3", "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "lavamoat": {"allowScripts": {"@lavamoat/preinstall-always-fail": false}}, "tsd": {"directory": "src"}}