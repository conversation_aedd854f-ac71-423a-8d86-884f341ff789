import { argentWallet } from './argentWallet/argentWallet';
import { bifrostWallet } from './bifrostWallet/bifrostWallet';
import { bitKeepWallet, bitgetWallet } from './bitgetWallet/bitgetWallet';
import { bitskiWallet } from './bitskiWallet/bitskiWallet';
import { braveWallet } from './braveWallet/braveWallet';
import { clvWallet } from './clvWallet/clvWallet';
import { coin98Wallet } from './coin98Wallet/coin98Wallet';
import { coinbaseWallet } from './coinbaseWallet/coinbaseWallet';
import { coreWallet } from './coreWallet/coreWallet';
import { dawnWallet } from './dawnWallet/dawnWallet';
import { desigWallet } from './desigWallet/desigWallet';
import { enkryptWallet } from './enkryptWallet/enkryptWallet';
import { foxWallet } from './foxWallet/foxWallet';
import { frameWallet } from './frameWallet/frameWallet';
import { frontierWallet } from './frontierWallet/frontierWallet';
import { imTokenWallet } from './imTokenWallet/imTokenWallet';
import { injectedWallet } from './injectedWallet/injectedWallet';
import { ledgerWallet } from './ledgerWallet/ledgerWallet';
import { metaMaskWallet } from './metaMaskWallet/metaMaskWallet';
import { mewWallet } from './mewWallet/mewWallet';
import { oktoWallet } from './oktoWallet/oktoWallet';
import { okxWallet } from './okxWallet/okxWallet';
import { omniWallet } from './omniWallet/omniWallet';
import { oneKeyWallet } from './oneKeyWallet/oneKeyWallet';
import { phantomWallet } from './phantomWallet/phantomWallet';
import { rabbyWallet } from './rabbyWallet/rabbyWallet';
import { rainbowWallet } from './rainbowWallet/rainbowWallet';
import { safeWallet } from './safeWallet/safeWallet';
import { safeheronWallet } from './safeheronWallet/safeheronWallet';
import { safepalWallet } from './safepalWallet/safepalWallet';
import { subWallet } from './subWallet/subWallet';
import { tahoWallet } from './tahoWallet/tahoWallet';
import { talismanWallet } from './talismanWallet/talismanWallet';
import { tokenPocketWallet } from './tokenPocketWallet/tokenPocketWallet';
import { tokenaryWallet } from './tokenaryWallet/tokenaryWallet';
import { trustWallet } from './trustWallet/trustWallet';
import { uniswapWallet } from './uniswapWallet/uniswapWallet';
import { walletConnectWallet } from './walletConnectWallet/walletConnectWallet';
import { xdefiWallet } from './xdefiWallet/xdefiWallet';
import { zealWallet } from './zealWallet/zealWallet';
import { zerionWallet } from './zerionWallet/zerionWallet';
export { argentWallet, bifrostWallet, bitgetWallet, bitKeepWallet, bitskiWallet, braveWallet, clvWallet, coin98Wallet, coinbaseWallet, coreWallet, dawnWallet, desigWallet, enkryptWallet, foxWallet, frameWallet, frontierWallet, imTokenWallet, injectedWallet, ledgerWallet, metaMaskWallet, mewWallet, oktoWallet, okxWallet, omniWallet, oneKeyWallet, phantomWallet, rabbyWallet, rainbowWallet, safeWallet, safeheronWallet, safepalWallet, subWallet, tahoWallet, talismanWallet, tokenaryWallet, tokenPocketWallet, trustWallet, uniswapWallet, walletConnectWallet, xdefiWallet, zealWallet, zerionWallet, };
