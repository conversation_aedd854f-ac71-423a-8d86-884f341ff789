"use client";
// src/wallets/walletConnectors/xdefiWallet/xdefiWallet.svg
var xdefiWallet_default = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyOCAyOCI+PGcgY2xpcC1wYXRoPSJ1cmwoI2EpIj48cGF0aCBmaWxsPSIjMzM1REU1IiBkPSJNMCAwaDI4djI4SDB6Ii8+PG1hc2sgaWQ9ImIiIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgeD0iMCIgeT0iMCIgbWFza1VuaXRzPSJ1c2VyU3BhY2VPblVzZSIgc3R5bGU9Im1hc2stdHlwZTpsdW1pbmFuY2UiPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0yOCAwSDB2MjhoMjhWMFoiLz48L21hc2s+PGcgbWFzaz0idXJsKCNiKSI+PHBhdGggZmlsbD0iIzMzNURFNSIgZD0iTTIyLjMxMyAwSDUuNjg4QTUuNjg3IDUuNjg3IDAgMCAwIDAgNS42ODh2MTYuNjI1QTUuNjg4IDUuNjg4IDAgMCAwIDUuNjg4IDI4aDE2LjYyNUE1LjY4OCA1LjY4OCAwIDAgMCAyOCAyMi4zMTJWNS42ODhBNS42ODggNS42ODggMCAwIDAgMjIuMzEyIDBaIi8+PHBhdGggZmlsbD0iI0VDRUNFQyIgZD0iTTI1LjM0NCAxMy4xOTNhMTEuMjgyIDExLjI4MiAwIDAgMC0xLjkzMy01LjUyMiAxMS41MiAxMS41MiAwIDAgMC00LjQ1OC0zLjg1NCAxMS43MyAxMS43MyAwIDAgMC0xMS40NS42NDFBMTEuNDYzIDExLjQ2MyAwIDAgMCAzLjUyIDguNzg1bC0uMDQ0LjA4OGE2LjM1NyA2LjM1NyAwIDAgMC0uNTg1IDEuODg0Yy0uMjczIDEuOTMyLjE1IDMuNjUxIDEuMjU3IDQuOTc1IDEuMjEgMS40NDcgMy4xOCAyLjMwOCA1LjU0NCAyLjQyMSAyLjg3OC4xNDMgNS43MzktLjYzIDcuNzctMi4wNWwxLjIwMy43MDVjLTEuMTUuOTgxLTMuODA5IDIuNzQ4LTguMjEzIDIuOTktNC45NDMuMjY5LTcuMDAzLTEuMzE0LTcuMDIzLTEuMzNsLS4zNDYuNDIxLS40NTcuNTRjLjA4OC4wNzMgMi4wNjMgMS42NDQgNi43MDMgMS42NDQuMzggMCAuNzggMCAxLjE5NS0uMDMyIDUuMzMtLjI5MyA4LjI2LTIuNTggOS4yODctMy41NjFsMS4wMDYuNjAyYTkuMTY1IDkuMTY1IDAgMCAxLTIuNDU5IDIuMjA2Yy0zLjM0NSAyLjEyNi03LjYgMi40LTEwLjU3OCAyLjI1MmwtLjA2MyAxLjI0Yy41LjAyNC45ODEuMDM1IDEuNDQ5LjAzNSA4LjM5MiAwIDExLjc4OC0zLjc2IDEyLjc0LTUuMTA5bC43ODYuNDYxYTEwLjIyIDEwLjIyIDAgMCAxLTMuMDA3IDMuMTk3IDEwLjQxNyAxMC40MTcgMCAwIDEtNC45NjcgMS43NzZsLjExIDEuMjM1YTExLjY5IDExLjY5IDAgMCAwIDUuNTc2LTEuOTkyIDExLjQ0NCAxMS40NDQgMCAwIDAgMy44NDMtNC40NSAxMS4yNSAxMS4yNSAwIDAgMCAxLjA5OC01Ljc0Wm0tNC44NDcuMTkxYTEgMSAwIDAgMS0xLjAwOC0uOTkzIDEgMSAwIDAgMSAxLjAwOC0uOTkyIDEgMSAwIDAgMSAxLjAwOS45OTIgMSAxIDAgMCAxLTEuMDA5Ljk5M1oiLz48L2c+PC9nPjxkZWZzPjxjbGlwUGF0aCBpZD0iYSI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTAgMGgyOHYyOEgweiIvPjwvY2xpcFBhdGg+PC9kZWZzPjwvc3ZnPg==";
export {
  xdefiWallet_default as default
};
