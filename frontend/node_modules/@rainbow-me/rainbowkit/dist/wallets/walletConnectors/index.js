"use client";
import {
  zerionWallet
} from "./chunk-EBWTDDFA.js";
import {
  zealWallet
} from "./chunk-LJ6A4ZAF.js";
import {
  tahoWallet
} from "./chunk-WG6TOL3W.js";
import {
  talismanWallet
} from "./chunk-TPZGT45G.js";
import {
  tokenaryWallet
} from "./chunk-V5NFDUUI.js";
import {
  tokenPocketWallet
} from "./chunk-2TLRAFPK.js";
import {
  trustWallet
} from "./chunk-KRB7QT2L.js";
import {
  uniswapWallet
} from "./chunk-GEJE7HDQ.js";
import {
  xdefiWallet
} from "./chunk-RWVPLIAV.js";
import {
  walletConnectWallet
} from "./chunk-AFQDNMHR.js";
import {
  oneKeyWallet
} from "./chunk-BBJKQTN3.js";
import {
  phantomWallet
} from "./chunk-XOGNDGKZ.js";
import {
  rabbyWallet
} from "./chunk-TNC2B7LX.js";
import {
  rainbowWallet
} from "./chunk-NXTGMMKC.js";
import {
  safeWallet
} from "./chunk-A6WSD4AS.js";
import {
  safeheronWallet
} from "./chunk-7WTKIVCW.js";
import {
  safepalWallet
} from "./chunk-6GAQUU2I.js";
import {
  subWallet
} from "./chunk-6UZUEWNI.js";
import {
  imTokenWallet
} from "./chunk-NP7YNZFK.js";
import {
  injectedWallet
} from "./chunk-7TYS3UTW.js";
import {
  ledgerWallet
} from "./chunk-O6GPXB7E.js";
import {
  metaMaskWallet
} from "./chunk-YY63ARSJ.js";
import {
  oktoWallet
} from "./chunk-PLQVPRFW.js";
import {
  mewWallet
} from "./chunk-MG4RCX4W.js";
import {
  okxWallet
} from "./chunk-QBOG4TU6.js";
import {
  omniWallet
} from "./chunk-7VAS62IJ.js";
import {
  bitKeepWallet,
  bitgetWallet
} from "./chunk-ILKXCMW2.js";
import {
  desigWallet
} from "./chunk-JGG2V4XA.js";
import {
  bitskiWallet
} from "./chunk-ZAV235NL.js";
import {
  enkryptWallet
} from "./chunk-3ZFYANJH.js";
import {
  dawnWallet
} from "./chunk-QJNS6IE4.js";
import {
  foxWallet
} from "./chunk-ARXBG5HI.js";
import {
  frameWallet
} from "./chunk-PWJ32OLJ.js";
import {
  frontierWallet
} from "./chunk-SXH7BZQ3.js";
import {
  argentWallet
} from "./chunk-6D5IWTK5.js";
import {
  clvWallet
} from "./chunk-FJC3HGLW.js";
import {
  bifrostWallet
} from "./chunk-HXHIVLJJ.js";
import {
  braveWallet
} from "./chunk-I5IK77LU.js";
import {
  coin98Wallet
} from "./chunk-SUTSTKXS.js";
import {
  coinbaseWallet
} from "./chunk-XDNOHDO2.js";
import "./chunk-MQYCNKY3.js";
import "./chunk-ZOLACFTK.js";
import {
  coreWallet
} from "./chunk-VNY6Z7PN.js";
import "./chunk-7IPLF2TT.js";
export {
  argentWallet,
  bifrostWallet,
  bitKeepWallet,
  bitgetWallet,
  bitskiWallet,
  braveWallet,
  clvWallet,
  coin98Wallet,
  coinbaseWallet,
  coreWallet,
  dawnWallet,
  desigWallet,
  enkryptWallet,
  foxWallet,
  frameWallet,
  frontierWallet,
  imTokenWallet,
  injectedWallet,
  ledgerWallet,
  metaMaskWallet,
  mewWallet,
  oktoWallet,
  okxWallet,
  omniWallet,
  oneKeyWallet,
  phantomWallet,
  rabbyWallet,
  rainbowWallet,
  safeWallet,
  safeheronWallet,
  safepalWallet,
  subWallet,
  tahoWallet,
  talismanWallet,
  tokenPocketWallet,
  tokenaryWallet,
  trustWallet,
  uniswapWallet,
  walletConnectWallet,
  xdefiWallet,
  zealWallet,
  zerionWallet
};
