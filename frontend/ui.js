// UI Management and Navigation
class UIManager {
    constructor() {
        this.currentPage = 'swap';
        this.isWalletConnected = false;
        this.userAddress = null;
        this.balances = {};
        this.settings = {
            slippage: CONFIG.PROTOCOL.DEFAULT_SLIPPAGE,
            deadline: CONFIG.PROTOCOL.DEFAULT_DEADLINE
        };

        this.initializeEventListeners();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const page = e.target.dataset.page;
                this.navigateToPage(page);
            });
        });

        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchTab(tab);
            });
        });

        // Modal controls
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeAllModals();
            }
        });

        // Settings
        document.querySelectorAll('.slippage-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setSlippage(parseFloat(e.target.dataset.slippage));
            });
        });

        // Custom slippage input
        const customSlippage = document.getElementById('customSlippage');
        if (customSlippage) {
            customSlippage.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                if (value >= 0 && value <= 50) {
                    this.setSlippage(value);
                }
            });
        }

        // Deadline input
        const deadline = document.getElementById('deadline');
        if (deadline) {
            deadline.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                if (value >= 1 && value <= 4320) {
                    this.settings.deadline = value;
                }
            });
        }
    }

    // Navigate to different pages
    navigateToPage(pageName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${pageName}"]`).classList.add('active');

        // Show/hide pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });
        document.getElementById(`${pageName}Page`).classList.add('active');

        this.currentPage = pageName;

        // Load page-specific data
        this.loadPageData(pageName);
    }

    // Switch tabs within pages
    switchTab(tabName) {
        const currentPageElement = document.querySelector('.page.active');

        // Update tab buttons
        currentPageElement.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        currentPageElement.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Show/hide tab content
        currentPageElement.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        currentPageElement.querySelector(`#${tabName}Tab, #${tabName}LiquidityTab, #${tabName}sTab`).classList.add('active');
    }

    // Load page-specific data
    async loadPageData(pageName) {
        switch (pageName) {
            case 'swap':
                await this.loadSwapData();
                break;
            case 'liquidity':
                await this.loadLiquidityData();
                break;
            case 'swap-forward':
                await this.loadSwapForwardData();
                break;
            case 'fees':
                await this.loadFeesData();
                break;
        }
    }

    // Load swap page data
    async loadSwapData() {
        if (this.isWalletConnected) {
            await this.updateTokenBalances();
        }
    }

    // Load liquidity page data
    async loadLiquidityData() {
        if (this.isWalletConnected) {
            await this.updateTokenBalances();
            await this.loadLiquidityPositions();
        }
    }

    // Load swap & forward page data
    async loadSwapForwardData() {
        if (this.isWalletConnected) {
            await this.updateTokenBalances();
        }
    }

    // Load fees page data
    async loadFeesData() {
        await this.loadFeeStatistics();
        if (this.isWalletConnected) {
            await this.loadClaimableFees();
            await this.checkAdminAccess();
        }
    }

    // Update token balances
    async updateTokenBalances() {
        try {
            if (!window.contractFactory || !this.userAddress) return;

            const contracts = window.contractFactory.getAllContracts();

            // Get ETH balance
            const ethBalance = await window.provider.getBalance(this.userAddress);
            this.balances.ETH = ethers.utils.formatEther(ethBalance);

            // Get UBA balance
            const ubaBalance = await contracts.ubaToken.getInstance().balanceOf(this.userAddress);
            this.balances.UBA = ethers.utils.formatEther(ubaBalance);

            // Update UI
            this.updateBalanceDisplay();

        } catch (error) {
            console.error('Error updating balances:', error);
        }
    }

    // Update balance display in UI
    updateBalanceDisplay() {
        // Update wallet banner
        const ethBalanceEl = document.getElementById('ethBalance');
        const ubaBalanceEl = document.getElementById('ubaBalance');

        if (ethBalanceEl) ethBalanceEl.textContent = parseFloat(this.balances.ETH || 0).toFixed(4);
        if (ubaBalanceEl) ubaBalanceEl.textContent = parseFloat(this.balances.UBA || 0).toFixed(4);

        // Update token input balances
        this.updateTokenInputBalances();
    }

    // Update token input balance displays
    updateTokenInputBalances() {
        const fromBalance = document.getElementById('fromBalance');
        const toBalance = document.getElementById('toBalance');

        if (fromBalance && window.currentTokenIn) {
            const balance = this.balances[window.currentTokenIn] || 0;
            fromBalance.textContent = `Balance: ${parseFloat(balance).toFixed(4)}`;
        }

        if (toBalance && window.currentTokenOut) {
            const balance = this.balances[window.currentTokenOut] || 0;
            toBalance.textContent = `Balance: ${parseFloat(balance).toFixed(4)}`;
        }
    }

    // Show status message
    showStatus(message, type = 'info', duration = 5000) {
        const statusElements = document.querySelectorAll('.status');

        statusElements.forEach(statusEl => {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.classList.remove('hidden');

            if (type === 'success' && duration > 0) {
                setTimeout(() => {
                    statusEl.classList.add('hidden');
                }, duration);
            }
        });
    }

    // Show loading overlay
    showLoading(message = 'Processing transaction...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.querySelector('.loading-text');

        if (text) text.textContent = message;
        if (overlay) overlay.classList.remove('hidden');
    }

    // Hide loading overlay
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.classList.add('hidden');
    }

    // Open token selector modal
    openTokenSelector(direction) {
        window.tokenSelectorDirection = direction;
        this.populateTokenList();
        this.initializeTokenSearch();
        document.getElementById('tokenModal').classList.remove('hidden');
    }

    // Close token selector modal
    closeTokenModal() {
        document.getElementById('tokenModal').classList.add('hidden');
        // Clear search input
        const searchInput = document.getElementById('tokenSearch');
        if (searchInput) searchInput.value = '';
    }

    // Initialize token search functionality
    initializeTokenSearch() {
        const searchInput = document.getElementById('tokenSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterTokenList(e.target.value);
            });

            searchInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    this.handleTokenAddressPaste(e.target.value);
                }, 100);
            });
        }
    }

    // Filter token list based on search query
    filterTokenList(query) {
        const tokenList = document.getElementById('tokenList');
        const tokenItems = tokenList.querySelectorAll('.token-item');

        if (!query) {
            tokenItems.forEach(item => item.style.display = 'flex');
            return;
        }

        const searchTerm = query.toLowerCase();
        tokenItems.forEach(item => {
            const symbol = item.querySelector('.token-symbol').textContent.toLowerCase();
            const name = item.querySelector('.token-name').textContent.toLowerCase();
            const address = item.dataset.address ? item.dataset.address.toLowerCase() : '';

            if (symbol.includes(searchTerm) || name.includes(searchTerm) || address.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });

        // Check if it's a valid Ethereum address
        if (this.isValidAddress(query)) {
            this.showCustomTokenImport(query);
        } else {
            this.hideCustomTokenImport();
        }
    }

    // Handle token address paste
    async handleTokenAddressPaste(address) {
        if (this.isValidAddress(address)) {
            try {
                await this.importCustomToken(address);
            } catch (error) {
                console.error('Error importing custom token:', error);
                this.showStatus('Gagal mengimpor token kustom', 'error');
            }
        }
    }

    // Check if address is valid Ethereum address
    isValidAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }

    // Show custom token import option
    showCustomTokenImport(address) {
        let importSection = document.getElementById('customTokenImport');
        if (!importSection) {
            importSection = document.createElement('div');
            importSection.id = 'customTokenImport';
            importSection.className = 'custom-token-import';
            document.getElementById('tokenList').parentNode.appendChild(importSection);
        }

        importSection.innerHTML = `
            <div class="custom-token-header">
                <h4>Import Token Kustom</h4>
                <p>Alamat: ${address}</p>
            </div>
            <button class="import-token-btn" onclick="window.uiManager.importCustomToken('${address}')">
                Import Token
            </button>
        `;
        importSection.style.display = 'block';
    }

    // Hide custom token import option
    hideCustomTokenImport() {
        const importSection = document.getElementById('customTokenImport');
        if (importSection) {
            importSection.style.display = 'none';
        }
    }

    // Import custom token
    async importCustomToken(address) {
        try {
            this.showLoading('Mengimpor token...');

            // Create contract instance to get token info
            const tokenContract = new ethers.Contract(
                address,
                CONTRACT_ABIS.ERC20,
                window.provider
            );

            // Get token details
            const [symbol, name, decimals] = await Promise.all([
                tokenContract.symbol(),
                tokenContract.name(),
                tokenContract.decimals()
            ]);

            // Add to CONFIG.TOKENS temporarily
            const tokenKey = symbol.toUpperCase();
            CONFIG.TOKENS[tokenKey] = {
                address: address,
                symbol: symbol,
                name: name,
                decimals: decimals,
                icon: '🪙',
                color: '#888888',
                isNative: false,
                isCustom: true
            };

            // Refresh token list
            this.populateTokenList();
            this.hideCustomTokenImport();

            this.hideLoading();
            this.showStatus(`Token ${symbol} berhasil diimpor!`, 'success');

        } catch (error) {
            this.hideLoading();
            console.error('Error importing token:', error);
            this.showStatus('Gagal mengimpor token. Pastikan alamat valid.', 'error');
        }
    }

    // Populate token list in modal
    populateTokenList(searchQuery = '') {
        const tokenList = document.getElementById('tokenList');
        tokenList.innerHTML = '';

        Object.values(CONFIG.TOKENS).forEach(token => {
            const tokenItem = document.createElement('div');
            tokenItem.className = 'token-item';
            tokenItem.dataset.address = token.address;
            tokenItem.innerHTML = `
                <div class="token-icon">${token.icon}</div>
                <div class="token-info">
                    <div class="token-symbol">${token.symbol}</div>
                    <div class="token-name">${token.name}</div>
                    ${token.isCustom ? '<span class="custom-badge">Custom</span>' : ''}
                </div>
                <div class="token-balance">${this.balances[token.symbol] || '0.0000'}</div>
            `;

            tokenItem.addEventListener('click', () => {
                this.selectToken(token.symbol);
            });

            tokenList.appendChild(tokenItem);
        });

        // Apply search filter if provided
        if (searchQuery) {
            this.filterTokenList(searchQuery);
        }
    }

    // Select token from modal
    selectToken(symbol) {
        if (window.tokenSelectorDirection === 'in') {
            window.currentTokenIn = symbol;
        } else {
            window.currentTokenOut = symbol;
        }

        this.updateTokenUI();
        this.closeTokenModal();

        // Trigger balance update
        this.updateTokenInputBalances();
    }

    // Update token UI elements
    updateTokenUI() {
        const tokenInIcon = document.getElementById('tokenInIcon');
        const tokenInSymbol = document.getElementById('tokenInSymbol');
        const tokenOutIcon = document.getElementById('tokenOutIcon');
        const tokenOutSymbol = document.getElementById('tokenOutSymbol');

        if (tokenInIcon && window.currentTokenIn) {
            const token = CONFIG.TOKENS[window.currentTokenIn];
            tokenInIcon.textContent = token.icon;
            tokenInSymbol.textContent = token.symbol;
        }

        if (tokenOutIcon && window.currentTokenOut) {
            const token = CONFIG.TOKENS[window.currentTokenOut];
            tokenOutIcon.textContent = token.icon;
            tokenOutSymbol.textContent = token.symbol;
        }
    }

    // Open settings modal
    openSettings() {
        document.getElementById('settingsModal').classList.remove('hidden');
    }

    // Close settings modal
    closeSettings() {
        document.getElementById('settingsModal').classList.add('hidden');
    }

    // Close all modals
    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    // Set slippage tolerance
    setSlippage(value) {
        this.settings.slippage = value;

        // Update UI
        document.querySelectorAll('.slippage-btn').forEach(btn => {
            btn.classList.remove('active');
            if (parseFloat(btn.dataset.slippage) === value) {
                btn.classList.add('active');
            }
        });

        // Clear custom input if using preset
        const customInput = document.getElementById('customSlippage');
        if (customInput && [0.1, 0.5, 1.0].includes(value)) {
            customInput.value = '';
        }
    }

    // Update wallet connection status
    updateWalletStatus(isConnected, address = null) {
        this.isWalletConnected = isConnected;
        this.userAddress = address;

        const connectBtn = document.getElementById('connectWallet');
        const walletBanner = document.getElementById('walletBanner');
        const walletAddressEl = document.getElementById('walletAddress');

        if (isConnected && address) {
            connectBtn.textContent = 'Connected';
            connectBtn.style.background = 'rgba(34, 197, 94, 0.2)';
            connectBtn.style.color = '#22c55e';

            walletBanner.classList.remove('hidden');
            walletAddressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;

            // Update balances
            this.updateTokenBalances();
        } else {
            connectBtn.textContent = 'Connect Wallet';
            connectBtn.style.background = '';
            connectBtn.style.color = '';

            walletBanner.classList.add('hidden');
            this.balances = {};
        }
    }

    // Placeholder functions for data loading
    async loadLiquidityPositions() {
        // TODO: Implement liquidity positions loading
        console.log('Loading liquidity positions...');
    }

    async loadFeeStatistics() {
        // TODO: Implement fee statistics loading
        console.log('Loading fee statistics...');
    }

    async loadClaimableFees() {
        // TODO: Implement claimable fees loading
        console.log('Loading claimable fees...');
    }

    async checkAdminAccess() {
        // TODO: Implement admin access check
        console.log('Checking admin access...');
    }
}

// Global UI Manager instance
window.uiManager = new UIManager();

// Global utility functions
window.openTokenSelector = (direction) => window.uiManager.openTokenSelector(direction);
window.closeTokenModal = () => window.uiManager.closeTokenModal();
window.openSettings = () => window.uiManager.openSettings();
window.closeSettings = () => window.uiManager.closeSettings();
window.switchTab = (tab) => window.uiManager.switchTab(tab);
